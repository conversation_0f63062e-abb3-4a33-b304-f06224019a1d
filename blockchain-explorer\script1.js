// Educational Blockchain Explorer - Interactive Features

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Set up search functionality
    setupSearch();
    
    // Set up navigation
    setupNavigation();
    
    // Set up wallet connection simulation
    setupWalletConnection();
    
    // Set up market data updates
    setupMarketData();
    
    // Set up trending coins interaction
    setupTrendingCoins();

    // Set up main sections interaction
    setupMainSections();

    // Set up footer interactions
    setupFooterInteractions();

    // Set up live statistics updates
    setupLiveStatistics();
}

// Search functionality
function setupSearch() {
    const searchContainer = document.querySelector('.search-container');
    
    if (searchContainer) {
        searchContainer.addEventListener('click', function() {
            // Simulate search modal opening
            showSearchModal();
        });
        
        // Add keyboard shortcut for search
        document.addEventListener('keydown', function(e) {
            if (e.key === '/' && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
                showSearchModal();
            }
        });
    }
}

function showSearchModal() {
    // Create a simple search modal
    const modal = document.createElement('div');
    modal.className = 'search-modal';
    modal.innerHTML = `
        <div class="search-modal-content">
            <div class="search-modal-header">
                <h3>Search Blockchain</h3>
                <button class="close-modal">&times;</button>
            </div>
            <input type="text" placeholder="Search transactions, addresses, blocks..." class="search-input">
            <div class="search-suggestions">
                <div class="suggestion-item">🔍 Search by transaction hash</div>
                <div class="suggestion-item">📍 Search by address</div>
                <div class="suggestion-item">🧱 Search by block number</div>
                <div class="suggestion-item">🪙 Search by token name</div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Focus on input
    const input = modal.querySelector('.search-input');
    input.focus();
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    // Close on escape key
    document.addEventListener('keydown', function escapeHandler(e) {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', escapeHandler);
        }
    });
    
    // Close on outside click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// Navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-links a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Simulate page navigation
            const page = this.textContent.toLowerCase();
            showNotification(`Navigating to ${page} page...`);
        });
    });
}

// Wallet connection simulation
function setupWalletConnection() {
    const connectBtn = document.querySelector('.connect-btn');
    
    if (connectBtn) {
        connectBtn.addEventListener('click', function() {
            // Simulate wallet connection
            simulateWalletConnection();
        });
    }
}

function simulateWalletConnection() {
    const connectBtn = document.querySelector('.connect-btn');
    const originalContent = connectBtn.innerHTML;
    
    // Show connecting state
    connectBtn.innerHTML = '<div class="wallet-icon">⏳</div><p>Connecting...</p>';
    connectBtn.style.pointerEvents = 'none';
    
    setTimeout(() => {
        // Show connected state
        connectBtn.innerHTML = '<div class="wallet-icon">✅</div><p>Connected</p>';
        connectBtn.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
        connectBtn.style.borderColor = 'var(--color-success)';
        
        showNotification('Wallet connected successfully!');
        
        // Reset after 3 seconds for demo purposes
        setTimeout(() => {
            connectBtn.innerHTML = originalContent;
            connectBtn.style.pointerEvents = 'auto';
            connectBtn.style.backgroundColor = '';
            connectBtn.style.borderColor = '';
        }, 3000);
    }, 2000);
}

// Market data updates
function setupMarketData() {
    // Simulate real-time price updates
    setInterval(updateMarketData, 10000); // Update every 10 seconds
}

function updateMarketData() {
    const priceElement = document.querySelector('.market-param .value');
    const changeElement = document.querySelector('.change-value');
    
    if (priceElement && changeElement) {
        // Generate random price change
        const currentPrice = parseFloat(priceElement.textContent);
        const change = (Math.random() - 0.5) * 0.2; // Random change between -0.1 and +0.1
        const newPrice = (currentPrice + change).toFixed(2);
        const percentChange = ((change / currentPrice) * 100).toFixed(2);
        
        // Update price
        priceElement.textContent = newPrice;
        
        // Update change indicator
        changeElement.textContent = `${percentChange >= 0 ? '+' : ''}${percentChange}%`;
        changeElement.className = `change-value ${percentChange >= 0 ? 'positive' : 'negative'}`;
        
        // Add flash effect
        priceElement.style.transition = 'color 0.3s ease';
        priceElement.style.color = percentChange >= 0 ? 'var(--color-success)' : 'var(--color-error)';
        
        setTimeout(() => {
            priceElement.style.color = '';
        }, 1000);
    }
}

// Trending coins interaction
function setupTrendingCoins() {
    const coinItems = document.querySelectorAll('.coin-item');
    
    coinItems.forEach(item => {
        item.addEventListener('click', function() {
            const coinName = this.querySelector('span').textContent;
            showNotification(`Viewing ${coinName} details...`);
            
            // Add click effect
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
        
        // Add hover effect
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

// Main sections interaction
function setupMainSections() {
    const sectionItems = document.querySelectorAll('.section-item');
    const partnerItems = document.querySelectorAll('.partner-item');

    sectionItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('.section-title').textContent;
            showNotification(`Opening ${title}...`);

            // Add click effect
            this.style.transform = 'translateX(8px)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });
    });

    partnerItems.forEach(item => {
        item.addEventListener('click', function() {
            const name = this.querySelector('.partner-name').textContent;
            showNotification(`Visiting ${name}...`);

            // Add click effect
            this.style.transform = 'translateY(-4px)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });
    });
}

// Footer interactions
function setupFooterInteractions() {
    const footerLinks = document.querySelectorAll('.footer-column a');
    const creditLogos = document.querySelectorAll('.credit-logo, .featured-logo');

    footerLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const text = this.textContent.trim();
            showNotification(`Navigating to ${text}...`);
        });
    });

    creditLogos.forEach(logo => {
        logo.addEventListener('click', function() {
            const text = this.textContent.trim();
            showNotification(`Visiting ${text}...`);
        });

        // Add hover effect
        logo.style.cursor = 'pointer';
        logo.addEventListener('mouseenter', function() {
            this.style.opacity = '0.7';
        });

        logo.addEventListener('mouseleave', function() {
            this.style.opacity = '1';
        });
    });
}

// Utility function to show notifications
function showNotification(message) {
    // Remove existing notification
    const existing = document.querySelector('.notification');
    if (existing) {
        existing.remove();
    }
    
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Add CSS for dynamic elements
const dynamicStyles = `
    .search-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .search-modal-content {
        background: white;
        border-radius: 12px;
        padding: 24px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .search-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }
    
    .close-modal {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-secondary);
    }
    
    .search-input {
        width: 100%;
        padding: 12px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        font-size: 16px;
        margin-bottom: 16px;
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--link-active-color-main);
    }
    
    .search-suggestions {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .suggestion-item {
        padding: 8px 12px;
        background-color: var(--background-secondary);
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .suggestion-item:hover {
        background-color: var(--border-color);
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--link-active-color-main);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: var(--shadow-medium);
        z-index: 1001;
        animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;

// Inject dynamic styles
const styleSheet = document.createElement('style');
styleSheet.textContent = dynamicStyles;
document.head.appendChild(styleSheet);

// Live Statistics Updates
function setupLiveStatistics() {
    // Initial values for simulation
    const initialValues = {
        epoch: 838,
        totalTxBlocks: 3885026005,
        totalTransactions: 11194566658,
        totalCheckpoints: 172886549,
        tpsCurrent: 24,
        cpsCurrent: 104,
        suiPrice: 3.92
    };

    // Update intervals (in milliseconds)
    const updateIntervals = {
        fast: 3000,    // 3 seconds for TPS/CPS
        medium: 10000, // 10 seconds for transactions
        slow: 30000    // 30 seconds for price
    };

    // Fast updates (TPS, CPS)
    setInterval(() => {
        updateTPS();
        updateCPS();
    }, updateIntervals.fast);

    // Medium updates (Transactions, Blocks, Checkpoints)
    setInterval(() => {
        updateTransactions();
        updateTxBlocks();
        updateCheckpoints();
    }, updateIntervals.medium);

    // Slow updates (Price)
    setInterval(() => {
        updateSuiPrice();
    }, updateIntervals.slow);

    // Update functions
    function updateTPS() {
        const tpsElement = document.querySelector('.stat-card:nth-child(11) .stat-value');
        if (tpsElement) {
            const currentValue = parseInt(tpsElement.textContent);
            const variation = Math.floor(Math.random() * 10) - 5; // -5 to +5
            const newValue = Math.max(1, currentValue + variation);
            tpsElement.textContent = newValue;

            // Update the change indicator
            const changeElement = tpsElement.parentElement.querySelector('.stat-change');
            if (changeElement) {
                const change = variation > 0 ? `+${variation} today` : `${variation} today`;
                changeElement.textContent = change;
                changeElement.className = variation >= 0 ? 'stat-change positive' : 'stat-change negative';
            }
        }
    }

    function updateCPS() {
        const cpsElement = document.querySelector('.stat-card:nth-child(12) .stat-value');
        if (cpsElement) {
            const currentValue = parseInt(cpsElement.textContent);
            const variation = Math.floor(Math.random() * 20) - 10; // -10 to +10
            const newValue = Math.max(1, currentValue + variation);
            cpsElement.textContent = newValue;

            // Update the change indicator
            const changeElement = cpsElement.parentElement.querySelector('.stat-change');
            if (changeElement) {
                const change = variation > 0 ? `+${variation} today` : `${variation} today`;
                changeElement.textContent = change;
                changeElement.className = variation >= 0 ? 'stat-change positive' : 'stat-change negative';
            }
        }
    }

    function updateTransactions() {
        const txElement = document.querySelector('.stat-card:nth-child(9) .stat-value');
        if (txElement) {
            const currentText = txElement.textContent.replace(/,/g, '');
            const currentValue = parseInt(currentText);
            const increment = Math.floor(Math.random() * 1000) + 500; // 500-1500 new transactions
            const newValue = currentValue + increment;
            txElement.textContent = newValue.toLocaleString();

            // Update 24H change
            const changeElement = txElement.parentElement.querySelector('.stat-change');
            if (changeElement) {
                const currentChange = parseInt(changeElement.textContent.replace(/[^\d]/g, ''));
                const newChange = currentChange + increment;
                changeElement.textContent = `24H +${newChange.toLocaleString()}`;
            }
        }
    }

    function updateTxBlocks() {
        const blocksElement = document.querySelector('.stat-card:nth-child(8) .stat-value');
        if (blocksElement) {
            const currentText = blocksElement.textContent.replace(/,/g, '');
            const currentValue = parseInt(currentText);
            const increment = Math.floor(Math.random() * 100) + 50; // 50-150 new blocks
            const newValue = currentValue + increment;
            blocksElement.textContent = newValue.toLocaleString();

            // Update 24H change
            const changeElement = blocksElement.parentElement.querySelector('.stat-change');
            if (changeElement) {
                const currentChange = parseInt(changeElement.textContent.replace(/[^\d]/g, ''));
                const newChange = currentChange + increment;
                changeElement.textContent = `24H +${newChange.toLocaleString()}`;
            }
        }
    }

    function updateCheckpoints() {
        const checkpointsElement = document.querySelector('.stat-card:nth-child(10) .stat-value');
        if (checkpointsElement) {
            const currentText = checkpointsElement.textContent.replace(/,/g, '');
            const currentValue = parseInt(currentText);
            const increment = Math.floor(Math.random() * 50) + 10; // 10-60 new checkpoints
            const newValue = currentValue + increment;
            checkpointsElement.textContent = newValue.toLocaleString();

            // Update 24H change
            const changeElement = checkpointsElement.parentElement.querySelector('.stat-change');
            if (changeElement) {
                const currentChange = parseInt(changeElement.textContent.replace(/[^\d]/g, ''));
                const newChange = currentChange + increment;
                changeElement.textContent = `24H +${newChange.toLocaleString()}`;
            }
        }
    }

    function updateSuiPrice() {
        const priceElement = document.querySelector('.stat-card:nth-child(6) .stat-value');
        if (priceElement) {
            const currentPrice = parseFloat(priceElement.textContent.replace('$', ''));
            const variation = (Math.random() - 0.5) * 0.2; // -0.1 to +0.1
            const newPrice = Math.max(0.1, currentPrice + variation);
            priceElement.textContent = `$${newPrice.toFixed(2)}`;

            // Update percentage change
            const changeElement = priceElement.parentElement.querySelector('.stat-change');
            if (changeElement) {
                const currentPercent = parseFloat(changeElement.textContent.replace(/[^\d.-]/g, ''));
                const newPercent = currentPercent + (variation / currentPrice * 100);
                const sign = newPercent >= 0 ? '+' : '';
                changeElement.textContent = `24H ${sign}${newPercent.toFixed(2)}%`;
                changeElement.className = newPercent >= 0 ? 'stat-change positive' : 'stat-change negative';
            }
        }
    }

    // Add visual indicator for live updates
    function addLiveIndicator() {
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            // Add live indicator to cards that update
            if ([5, 7, 8, 9, 10, 11].includes(index)) { // Price, TxBlocks, Transactions, Checkpoints, TPS, CPS
                const indicator = document.createElement('div');
                indicator.className = 'live-indicator';
                indicator.innerHTML = '🔴';
                indicator.style.cssText = `
                    position: absolute;
                    top: 8px;
                    left: 8px;
                    font-size: 8px;
                    animation: pulse 2s infinite;
                `;
                card.appendChild(indicator);
            }
        });

        // Add pulse animation
        const pulseStyle = document.createElement('style');
        pulseStyle.textContent = `
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.3; }
            }
        `;
        document.head.appendChild(pulseStyle);
    }

    // Initialize live indicators
    addLiveIndicator();

    // Initialize DeFi and DEX data updates
    initializeDeFiUpdates();
    initializeBottomStatsUpdates();
}

// DeFi Protocol Updates
function initializeDeFiUpdates() {
    const defiData = {
        'Suilend': { base: 693133195, variance: 0.02 },
        'NAVI Protocol': { base: 684300780, variance: 0.015 },
        'SpringSui': { base: 272875785, variance: 0.025 },
        'Haedal': { base: 192300102, variance: 0.02 },
        'Bluefin': { base: 183129733, variance: 0.018 }
    };

    const dexData = {
        'Pool:USDC / USDT': { base: 38495209, change: 12 },
        'Pool:USDT / USDC': { base: 38287038, change: 8 },
        'Pool:USDC / SUI': { base: 30259575, change: 20 },
        'Pool:SUI / USDC': { base: 28596204, change: 6 },
        'Pool:USDT / USDC': { base: 25163694, change: -34 }
    };

    const collectionsData = {
        'Pawtato Land': { base: 1766, variance: 0.1 },
        'Popkins': { base: 911, variance: 0.15 },
        'Bored Toilet Club': { base: 631, variance: 0.12 },
        'Prime Machin': { base: 572, variance: 0.08 },
        'Mystic Yeti': { base: 327, variance: 0.2 }
    };

    // Update DeFi TVL values every 15 seconds
    setInterval(() => {
        updateDeFiValues(defiData);
    }, 15000);

    // Update DEX volumes every 20 seconds
    setInterval(() => {
        updateDexValues(dexData);
    }, 20000);

    // Update collection volumes every 25 seconds
    setInterval(() => {
        updateCollectionValues(collectionsData);
    }, 25000);
}

function updateDeFiValues(defiData) {
    Object.keys(defiData).forEach(protocolName => {
        const data = defiData[protocolName];
        const variation = (Math.random() - 0.5) * 2 * data.variance;
        const newValue = Math.floor(data.base * (1 + variation));

        const defiItems = document.querySelectorAll('.defi-item');
        defiItems.forEach(item => {
            const nameElement = item.querySelector('.defi-name');
            if (nameElement && nameElement.textContent === protocolName) {
                const tvlElement = item.querySelector('.defi-tvl');
                if (tvlElement) {
                    tvlElement.textContent = `TVL $${newValue.toLocaleString()}`;
                }
            }
        });

        // Update base value for next iteration
        defiData[protocolName].base = newValue;
    });
}

function updateDexValues(dexData) {
    Object.keys(dexData).forEach(pairName => {
        const data = dexData[pairName];
        const volumeVariation = (Math.random() - 0.5) * 0.1;
        const changeVariation = (Math.random() - 0.5) * 4;

        const newVolume = Math.floor(data.base * (1 + volumeVariation));
        const newChange = Math.max(-50, Math.min(50, data.change + changeVariation));

        const dexItems = document.querySelectorAll('.dex-item');
        dexItems.forEach(item => {
            const pairElement = item.querySelector('.dex-pair');
            if (pairElement && pairElement.textContent === pairName) {
                const volumeElement = item.querySelector('.dex-volume');
                if (volumeElement) {
                    const changeClass = newChange >= 0 ? 'positive' : 'negative';
                    const changeSign = newChange >= 0 ? '+' : '';
                    volumeElement.innerHTML = `Vol. $${newVolume.toLocaleString()} <span class="change ${changeClass}">${changeSign}${Math.round(newChange)}%</span>`;
                }
            }
        });

        // Update base values
        dexData[pairName].base = newVolume;
        dexData[pairName].change = newChange;
    });
}

function updateCollectionValues(collectionsData) {
    Object.keys(collectionsData).forEach(collectionName => {
        const data = collectionsData[collectionName];
        const variation = (Math.random() - 0.5) * 2 * data.variance;
        const newValue = Math.max(1, Math.floor(data.base * (1 + variation)));

        const collectionItems = document.querySelectorAll('.collection-item');
        collectionItems.forEach(item => {
            const nameElement = item.querySelector('.collection-name');
            if (nameElement && nameElement.textContent === collectionName) {
                const volumeElement = item.querySelector('.collection-volume');
                if (volumeElement) {
                    volumeElement.textContent = `Vol. $${newValue.toLocaleString()}`;
                }
            }
        });

        // Update base value
        collectionsData[collectionName].base = newValue;
    });
}

// Bottom Statistics Updates
function initializeBottomStatsUpdates() {
    const bottomStatsData = {
        'TVL': { base: **********, variance: 0.005 },
        'New Accounts': { base: 8750933, variance: 0.01 },
        'New NFTs': { base: 1194629, variance: 0.02 },
        'New Coins': { base: 1881, variance: 0.05 }
    };

    // Update bottom stats every 12 seconds
    setInterval(() => {
        updateBottomStats(bottomStatsData);
        updateMiniCharts();
    }, 12000);
}

function updateBottomStats(statsData) {
    Object.keys(statsData).forEach(statName => {
        const data = statsData[statName];
        const variation = (Math.random() - 0.5) * 2 * data.variance;
        const newValue = Math.floor(data.base * (1 + variation));

        const statItems = document.querySelectorAll('.stat-chart-item');
        statItems.forEach(item => {
            const labelElement = item.querySelector('.stat-chart-label');
            if (labelElement && labelElement.textContent === statName) {
                const valueElement = item.querySelector('.stat-chart-value');
                if (valueElement) {
                    if (statName === 'TVL') {
                        valueElement.textContent = `$${newValue.toLocaleString()}`;
                    } else {
                        valueElement.textContent = newValue.toLocaleString();
                    }
                }
            }
        });

        // Update base value
        statsData[statName].base = newValue;
    });
}

function updateMiniCharts() {
    const chartContainers = document.querySelectorAll('.mini-chart-container');

    chartContainers.forEach((container, index) => {
        const svg = container.querySelector('.mini-chart');
        const strokePath = svg.querySelector('path[stroke]');
        const fillPath = svg.querySelector('path[fill]');

        if (strokePath && fillPath) {
            // Generate realistic trend data based on chart type
            const points = generateChartPoints(index);

            // Create stroke path (line)
            const strokePathData = createSmoothPath(points);
            strokePath.setAttribute('d', strokePathData);

            // Create fill path (area under curve)
            const fillPathData = strokePathData + ' L100,30 L0,30 Z';
            fillPath.setAttribute('d', fillPathData);
        }
    });
}

function generateChartPoints(chartType) {
    const points = [];
    let trend = 0;

    // Different trend patterns for different chart types
    switch(chartType) {
        case 0: // TVL - generally upward trend
            trend = 0.8;
            break;
        case 1: // New Accounts - moderate growth
            trend = 0.6;
            break;
        case 2: // New NFTs - volatile
            trend = 0.3;
            break;
        case 3: // New Coins - strong upward
            trend = 0.9;
            break;
    }

    let currentY = 25; // Start from bottom

    for (let i = 0; i <= 100; i += 12) {
        // Add trend and some randomness
        const trendEffect = -trend * 0.3; // Negative because lower Y = higher on chart
        const randomEffect = (Math.random() - 0.5) * 4;

        currentY += trendEffect + randomEffect;

        // Keep within bounds
        currentY = Math.max(8, Math.min(26, currentY));

        points.push({ x: i, y: currentY });
    }

    return points;
}

function createSmoothPath(points) {
    if (points.length < 2) return '';

    let path = `M${points[0].x},${points[0].y}`;

    for (let i = 1; i < points.length; i++) {
        const current = points[i];
        const previous = points[i - 1];

        if (i === 1) {
            // First curve
            path += ` L${current.x},${current.y}`;
        } else {
            // Smooth curves using quadratic bezier
            const controlX = (previous.x + current.x) / 2;
            const controlY = (previous.y + current.y) / 2;
            path += ` Q${controlX},${controlY} ${current.x},${current.y}`;
        }
    }

    return path;
}
