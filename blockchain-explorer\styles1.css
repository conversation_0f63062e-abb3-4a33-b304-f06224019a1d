/* CSS Variables */
:root {
    --background-solid-1: #ffffff;
    --text-main: #182435;
    --text-secondary: #767A81;
    --gray-medium: #9CA3AF;
    --link-active-color-main: #7e9bfc;
    --color-error: #EF4444;
    --color-success: #10B981;
    --border-color: #E5E7EB;
    --background-secondary: #F7F8F8;
    --shadow-light: 0px 2px 6px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0px 4px 12px rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-solid-1);
    color: var(--text-main);
    line-height: 1.5;
    overflow-y: scroll;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: var(--background-solid-1);
    border-bottom: 1px solid #F3F6F8;
    position: sticky;
    top: 0;
    z-index: 100;
}

.explorer-header {
    padding: 8px 0;
}

.header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 48px;
}

.header-info {
    display: flex;
    align-items: center;
}

.market-params {
    display: flex;
    gap: 32px;
    align-items: center;
}

.market-param {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
}

.market-param .title {
    color: #767A81;
    font-weight: 500;
    font-size: 13px;
}

.market-param .value {
    font-weight: 600;
    color: var(--text-main);
    font-size: 13px;
}

.market-param .value-icon {
    color: #767A81;
    font-size: 10px;
    margin-right: 2px;
}

.change-value {
    margin-left: 4px;
}

.change-value.negative {
    color: #FF794B;
    font-weight: 600;
    font-size: 12px;
}

.change-value.positive {
    color: var(--color-success);
    font-weight: 600;
    font-size: 12px;
}

.header-panel {
    display: flex;
    align-items: center;
    gap: 8px;
}

.connect-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #ffffff;
    border: 1px solid #ffffff;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: #7e9bfc
}

.connect-btn:hover {
    background-color: #fff;
}

.connect-btn svg {
    width: 20px;
    height: 20px;
}

.connect-btn p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.network-switcher {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 0 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-main);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-indicator.green {
    background-color: var(--color-success);
}

.blockchain-switcher {
    position: relative;
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-icon {
    font-size: 18px;
    color: var(--text-main);
}

.active-dot {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 6px;
    height: 6px;
    background-color: var(--link-active-color-main);
    border-radius: 50%;
}

/* Navigation Styles */
.project-header {
    background-color: var(--background-solid-1);
    border-bottom: none;
}

.navigation {
    padding: 0;
    border-bottom: 1px solid #F3F6F8;
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    width: 100%;
}

.logo {
    flex-shrink: 0;
}

.logo img {
    height: 36px;
}

.navbar {
    display: flex;
    justify-content: flex-end;
    flex: 1;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 8px;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-links li {
    margin: 0;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-main);
    font-weight: 500;
    font-size: 16px;
    padding: 12px 16px;
    transition: all 0.2s ease;
    position: relative;
    display: block;
    border-radius: 6px;
    white-space: nowrap;
}

.nav-links a:hover {
    color: var(--link-active-color-main);
    background-color: rgba(79, 70, 229, 0.05);
}

.nav-links a.active {
    color: var(--link-active-color-main);
    background-color: rgba(79, 70, 229, 0.08);
    font-weight: 600;
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background-color: var(--link-active-color-main);
    border-radius: 2px;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
}

/* Banner Styles */
.banner {
    background-color: #7e9bfc;
    background-image: url('ika.png');
    background-repeat: repeat;
    background-position: 0 0;
    background-size: cover;
    color: white;
    padding: 12px 0;
    margin: 16px 0 0;
    border-radius: 0;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    text-align: center;
    position: relative;
    z-index: 2;
    margin: 0 auto;
    padding: 0 60px;
    max-width: 1200px;
}

.banner-text {
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    white-space: nowrap;
    z-index: 3;
    position: relative;
    color: white;
}

.banner-text strong {
    font-weight: 800;
}

.banner-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
    position: relative;
    z-index: 4;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 8px;
    backdrop-filter: blur(4px);
}

.banner-logo img {
    height: 64px;
    width: auto;
    max-width: 120px;
    filter: brightness(1.2) contrast(1.2) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    object-fit: contain;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
    animation: shimmer 4s infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Explorer Plate */
.explorer-plate {
    background-color: #e8f0ff;
    background-image: url('back.png');
    background-repeat: repeat;
    background-position: 0 0;
    background-size: cover;
    color: white;
    padding: 48px 32px;
    border-radius: 0;
    margin: 0 0 32px;
    text-align: center;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.explorer-plate::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(232, 240, 255, 0.3);
    z-index: 1;
}

.explorer-plate > * {
    position: relative;
    z-index: 2;
}

.explorer-plate .title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 32px;
    color: var(--text-main);
    text-shadow: none;
}

.search-container {
    display: flex;
    align-items: center;
    background-color: var(--background-solid-1);
    color: var(--text-main);
    padding: 16px;
    border-radius: 12px;
    margin: 0 auto 32px;
    max-width: 600px;
    gap: 12px;
}

.search-text {
    flex: 1;
    color: var(--gray-medium);
}

.search-shortcut {
    background-color: var(--background-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.trending-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.trending-coins {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.coin-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.coin-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

/* Updated Trending Styles */
.trending-label {
    color: var(--text-main);
    font-size: 14px;
    font-weight: 500;
}

.coin-item {
    background-color: rgba(255, 255, 255, 0.8);
    color: var(--text-main);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 4px 12px;
    font-size: 13px;
}

.coin-item.sui { background-color: #4F46E5; color: white; }
.coin-item.blue { background-color: #3B82F6; color: white; }
.coin-item.wbtc { background-color: #F7931A; color: white; }
.coin-item.cetus { background-color: #10B981; color: white; }
.coin-item.usdc { background-color: #2775CA; color: white; }
.coin-item.xsui { background-color: #6366F1; color: white; }
.coin-item.wal { background-color: #059669; color: white; }
.coin-item.deep { background-color: #1E40AF; color: white; }
.coin-item.usdt { background-color: #26A17B; color: white; }

.coin-symbol {
    font-size: 13px;
    font-weight: 600;
}

/* News Section */
.news-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 32px;
    padding: 16px 24px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    position: relative;
    z-index: 2;
}

.news-item {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.news-icon {
    font-size: 16px;
}

.news-text {
    color: var(--text-main);
    font-weight: 600;
    font-size: 14px;
}

.news-category, .news-date, .news-type, .news-updates {
    color: var(--text-secondary);
    font-size: 12px;
    margin-left: 8px;
}

.news-hub {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: #FF6B35;
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
}

.news-hub-icon {
    font-size: 14px;
}

/* Statistics Section */
.statistics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin: 24px 0;
    margin-left: 40px;
}

.stat-card {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 12px 0;
    position: relative;
    transition: all 0.2s ease;
}

.stat-card:hover {
    opacity: 0.8;
}

/* Add borders starting from row 2 (cards 7 and onwards) */
.stat-card:nth-child(n+7) {
    background-color: var(--background-solid-1);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px 20px;
    margin: 8px;
    border-bottom: 1px solid var(--border-color);
}

.stat-card:nth-child(n+7):hover {
    border-color: var(--link-active-color-main);
    box-shadow: 0 2px 8px rgba(126, 155, 252, 0.1);
    opacity: 1;
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
}

.stat-info {
    color: var(--text-secondary);
    font-size: 10px;
    opacity: 0.6;
    cursor: help;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-main);
    margin-bottom: 4px;
    line-height: 1.2;
}

.stat-value.blue {
    color: var(--link-active-color-main);
}

.stat-change {
    font-size: 10px;
    font-weight: 600;
    margin-bottom: 4px;
}

.stat-change.positive {
    color: #10B981;
}

.stat-change.negative {
    color: #EF4444;
}

.stat-subtext {
    color: var(--text-secondary);
    font-size: 10px;
    margin-bottom: 2px;
}

.stat-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 9px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.stat-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 16px;
    color: var(--link-active-color-main);
    opacity: 0.8;
}

.chart-card {
    position: relative;
}

.price-chart {
    margin-top: 12px;
}

.chart-label {
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0.7;
}

.mini-chart {
    position: absolute;
    bottom: 16px;
    right: 16px;
    font-size: 14px;
    opacity: 0.6;
}

/* Responsive Statistics Grid */
@media (max-width: 768px) {
    .statistics-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .stat-card {
        padding: 16px;
    }

    .stat-value {
        font-size: 20px;
    }
}

/* Latest News */
.latest-news {
    background-color: var(--background-secondary);
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 32px;
}

.news-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
}

.news-content {
    color: var(--text-secondary);
}

/* Main Sections */
.main-sections {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 48px;
    margin: 48px 0;
    padding: 32px 0;
}

.section-column {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.section-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;
    padding: 12px;
}

.section-item:hover {
    background-color: var(--background-secondary);
    transform: translateX(4px);
}

.section-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.section-title {
    font-weight: 500;
    color: var(--text-main);
}

/* Partners Section */
.partners {
    gap: 20px;
}

.partner-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.partner-item:hover {
    background-color: var(--background-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.partner-logo {
    flex-shrink: 0;
}

.logo-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    color: white;
}

.logo-circle.black {
    background-color: #000000;
}

.logo-circle.blue {
    background-color: #3B82F6;
}

.logo-circle.dark {
    background-color: #1F2937;
}

.logo-circle.orange {
    background-color: #F59E0B;
}

.partner-info {
    flex: 1;
}

.partner-name {
    font-weight: 600;
    color: var(--text-main);
    font-size: 14px;
    margin-bottom: 2px;
}

.partner-desc {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Footer Styles */
.footer {
    background-color: var(--background-secondary);
    border-top: 1px solid var(--border-color);
    margin-top: 64px;
    padding: 48px 0 24px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 48px;
    margin-bottom: 48px;
}

.footer-brand {
    max-width: 300px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.footer-logo img {
    height: 36px;
    width: auto;
}

.footer-description {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 14px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
}

.footer-column h4 {
    font-weight: 600;
    color: var(--text-main);
    margin-bottom: 16px;
    font-size: 16px;
}

.footer-column ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-column a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.footer-column a:hover {
    color: var(--link-active-color-main);
}

.social-icon {
    font-size: 12px;
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 24px;
}

.footer-credits {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    align-items: center;
}

.credit-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.credit-label {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.credit-logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.staketab-logo,
.blockberry-logo {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-main);
}

.featured-logos {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.featured-logo {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-wrapper {
        flex-direction: column;
        gap: 16px;
    }

    .nav-wrapper {
        flex-direction: column;
        gap: 24px;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 16px;
    }

    .explorer-plate .title {
        font-size: 24px;
    }

    .trending-wrapper {
        flex-direction: column;
        gap: 12px;
    }

    .main-sections {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .footer-credits {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .featured-logos {
        flex-direction: column;
        gap: 8px;
    }
}

/* DeFi, DEX Pools, and Collections Section */
.defi-section-wrapper {
    background-color: var(--background-solid-1);
    padding: 3rem 0;
    margin: 2rem 0;
}

.defi-section {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin: 0;
}

.defi-column, .dex-column, .collections-column {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-main);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-indicator {
    font-size: 0.75rem;
    color: var(--text-secondary);
    background: var(--background-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 400;
}

/* DeFi Items */
.defi-list, .dex-list, .collections-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.defi-item, .dex-item, .collection-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    transition: all 0.2s ease;
    border-radius: 8px;
    min-height: 60px;
}

.defi-item:hover, .dex-item:hover, .collection-item:hover {
    background-color: var(--background-secondary);
    transform: translateX(2px);
}

.defi-icon, .collection-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.protocol-icon, .collection-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.defi-info, .dex-info, .collection-info {
    flex: 1;
}

.defi-name, .dex-pair, .collection-name {
    font-weight: 500;
    color: var(--text-main);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.defi-tvl, .dex-volume, .collection-volume {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* DEX Items */
.dex-icons {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-shrink: 0;
    min-width: 60px;
}

.token-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    flex-shrink: 0;
}

.token-icon.small {
    width: 16px;
    height: 16px;
    margin-left: 0.25rem;
}

.change {
    font-weight: 500;
    font-size: 0.75rem;
}

.change.positive {
    color: #10b981;
}

.change.negative {
    color: #ef4444;
}

/* Section More Links */
.section-more {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.more-link {
    color: var(--link-active-color-main);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.more-link:hover {
    text-decoration: underline;
}

/* Bottom Statistics with Charts */
.bottom-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin: 3rem 0;
}

.stat-chart-item {
    background: var(--background-solid-1);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    transition: all 0.2s ease;
}

.stat-chart-item:hover {
    border-color: var(--link-active-color-main);
    box-shadow: 0 4px 12px rgba(126, 155, 252, 0.15);
}

.stat-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.stat-chart-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-chart-info {
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: help;
}

.stat-chart-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-main);
    margin-bottom: 0.5rem;
}

.stat-chart-period {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.mini-chart-container {
    height: 40px;
    width: 100%;
    margin-top: 0.5rem;
    overflow: hidden;
    border-radius: 4px;
    background-color: #f8fafc;
}

.chart-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.stat-chart-item:hover .chart-image {
    transform: scale(1.02);
    opacity: 0.9;
}

/* Responsive Design for New Sections */
@media (max-width: 1024px) {
    .defi-section {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .defi-section-wrapper {
        padding: 2rem 0;
    }

    .bottom-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .bottom-stats {
        grid-template-columns: 1fr;
    }

    .defi-column, .dex-column, .collections-column {
        padding: 1rem;
    }

    .stat-chart-item {
        padding: 1rem;
    }
}
