class UltimateTicTacToe {
    constructor() {
        // Game state
        this.board = Array(9).fill('');
        this.currentPlayer = 'X';
        this.gameActive = false;
        this.gameMode = 'friend';
        this.difficulty = 'medium';
        this.soundEnabled = true;
        this.theme = 'light';

        // Player info
        this.players = {
            X: { name: 'Player 1', score: 0 },
            O: { name: 'Player 2', score: 0 }
        };

        // Game elements
        this.welcomeScreen = document.getElementById('welcome-screen');
        this.gameContainer = document.getElementById('game-container');
        this.statusElement = document.getElementById('status');
        this.boardElement = document.getElementById('game-board');
        this.victoryModal = document.getElementById('victory-modal');

        // Winning combinations
        this.winningConditions = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
            [0, 4, 8], [2, 4, 6] // Diagonals
        ];

        // Sound effects (using Web Audio API)
        this.sounds = this.createSounds();

        this.initializeWelcomeScreen();
        this.initializeGameControls();
        this.loadSettings();
    }

    createSounds() {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        const createTone = (frequency, duration, type = 'sine') => {
            return () => {
                if (!this.soundEnabled) return;

                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = frequency;
                oscillator.type = type;

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            };
        };

        return {
            move: createTone(800, 0.1),
            win: createTone(1000, 0.5),
            tie: createTone(400, 0.3),
            click: createTone(600, 0.05),
            error: createTone(200, 0.2)
        };
    }

    initializeWelcomeScreen() {
        // Mode selection
        const modeButtons = document.querySelectorAll('.mode-btn');
        modeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.sounds.click();
                modeButtons.forEach(b => b.classList.remove('selected'));
                btn.classList.add('selected');
                this.gameMode = btn.dataset.mode;
                this.updatePlayerInputs();
            });
        });

        // Start game button
        document.getElementById('start-game-btn').addEventListener('click', () => {
            this.sounds.click();
            this.startGame();
        });

        // Set default mode
        modeButtons[0].click();
    }

    updatePlayerInputs() {
        const player2Input = document.getElementById('player2-input');
        const player2Label = player2Input.querySelector('label');
        const player2Field = document.getElementById('player2-name');

        if (this.gameMode === 'friend') {
            player2Input.style.display = 'block';
            player2Label.textContent = 'Player 2 (O):';
            player2Field.placeholder = "Enter friend's name";
        } else {
            player2Input.style.display = 'block';
            player2Label.textContent = 'AI Opponent (O):';
            const difficultyNames = {
                easy: 'Easy AI',
                medium: 'Medium AI',
                hard: 'Hard AI'
            };
            player2Field.value = difficultyNames[this.gameMode] || 'AI';
            player2Field.disabled = true;
            this.difficulty = this.gameMode;
        }
    }

    initializeGameControls() {
        // Restart button
        document.getElementById('restart-btn').addEventListener('click', () => {
            this.sounds.click();
            this.restartRound();
        });

        // Reset scores button
        document.getElementById('reset-scores-btn').addEventListener('click', () => {
            this.sounds.click();
            this.resetScores();
        });

        // Sound toggle
        document.getElementById('sound-toggle').addEventListener('click', () => {
            this.toggleSound();
        });

        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => {
            this.sounds.click();
            this.toggleTheme();
        });

        // Back to menu
        document.getElementById('back-to-menu').addEventListener('click', () => {
            this.sounds.click();
            this.backToMenu();
        });

        // Victory modal buttons
        document.getElementById('play-again-btn').addEventListener('click', () => {
            this.sounds.click();
            this.closeVictoryModal();
            this.restartRound();
        });

        document.getElementById('change-mode-btn').addEventListener('click', () => {
            this.sounds.click();
            this.closeVictoryModal();
            this.backToMenu();
        });
    }

    startGame() {
        // Get player names
        const player1Name = document.getElementById('player1-name').value.trim() || 'Player 1';
        const player2Name = document.getElementById('player2-name').value.trim() ||
            (this.gameMode === 'friend' ? 'Player 2' : `${this.gameMode.charAt(0).toUpperCase() + this.gameMode.slice(1)} AI`);

        this.players.X.name = player1Name;
        this.players.O.name = player2Name;

        // Update UI
        document.getElementById('player1-display').textContent = player1Name;
        document.getElementById('player2-display').textContent = player2Name;

        // Show game screen
        this.welcomeScreen.classList.add('hidden');
        this.gameContainer.classList.remove('hidden');
        this.gameContainer.classList.add('fade-in');

        // Initialize game
        this.gameActive = true;
        this.updateDifficultyIndicator();
        this.createBoard();
        this.updateStatus();
        this.updateScoreboard();
    }

    createBoard() {
        const cells = this.boardElement.querySelectorAll('.cell');
        cells.forEach((cell, index) => {
            cell.replaceWith(cell.cloneNode(true)); // Remove old listeners
        });

        const newCells = this.boardElement.querySelectorAll('.cell');
        newCells.forEach((cell, index) => {
            cell.addEventListener('click', () => this.handleCellClick(index));
        });
    }

    handleCellClick(index) {
        if (this.board[index] !== '' || !this.gameActive) {
            this.sounds.error();
            return;
        }

        this.makeMove(index);

        if (this.gameActive && this.gameMode !== 'friend' && this.currentPlayer === 'O') {
            setTimeout(() => this.makeAIMove(), 500);
        }
    }

    makeMove(index) {
        this.board[index] = this.currentPlayer;
        const cell = this.boardElement.children[index];
        cell.textContent = this.currentPlayer;
        cell.classList.add(this.currentPlayer.toLowerCase());

        this.sounds.move();
        this.checkResult();
    }

    checkResult() {
        let roundWon = false;
        let winningCombination = null;

        // Check for win
        for (let i = 0; i < this.winningConditions.length; i++) {
            const [a, b, c] = this.winningConditions[i];
            if (this.board[a] && this.board[a] === this.board[b] && this.board[a] === this.board[c]) {
                roundWon = true;
                winningCombination = this.winningConditions[i];
                break;
            }
        }

        if (roundWon) {
            this.gameActive = false;
            this.players[this.currentPlayer].score++;
            this.highlightWinningCells(winningCombination);
            this.boardElement.classList.add('game-over');
            this.sounds.win();
            this.updateScoreboard();
            this.showVictoryModal('win');
            return;
        }

        // Check for tie
        if (!this.board.includes('')) {
            this.gameActive = false;
            this.boardElement.classList.add('game-over');
            this.sounds.tie();
            this.showVictoryModal('tie');
            return;
        }

        this.switchPlayer();
    }

    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 'X' ? 'O' : 'X';
        this.updateStatus();
    }

    makeAIMove() {
        if (!this.gameActive || this.currentPlayer !== 'O') return;

        let move;
        switch (this.difficulty) {
            case 'easy':
                move = this.getRandomMove();
                break;
            case 'medium':
                move = this.getMediumAIMove();
                break;
            case 'hard':
                move = this.getHardAIMove();
                break;
            default:
                move = this.getRandomMove();
        }

        if (move !== -1) {
            this.makeMove(move);
        }
    }

    getRandomMove() {
        const availableMoves = this.board
            .map((cell, index) => cell === '' ? index : null)
            .filter(val => val !== null);

        return availableMoves.length > 0
            ? availableMoves[Math.floor(Math.random() * availableMoves.length)]
            : -1;
    }

    getMediumAIMove() {
        // Try to win first
        for (let i = 0; i < 9; i++) {
            if (this.board[i] === '') {
                this.board[i] = 'O';
                if (this.checkWinCondition('O')) {
                    this.board[i] = '';
                    return i;
                }
                this.board[i] = '';
            }
        }

        // Block player from winning
        for (let i = 0; i < 9; i++) {
            if (this.board[i] === '') {
                this.board[i] = 'X';
                if (this.checkWinCondition('X')) {
                    this.board[i] = '';
                    return i;
                }
                this.board[i] = '';
            }
        }

        // Take center if available
        if (this.board[4] === '') return 4;

        // Take corners
        const corners = [0, 2, 6, 8];
        const availableCorners = corners.filter(i => this.board[i] === '');
        if (availableCorners.length > 0) {
            return availableCorners[Math.floor(Math.random() * availableCorners.length)];
        }

        // Random move
        return this.getRandomMove();
    }

    getHardAIMove() {
        // Use minimax algorithm for unbeatable AI
        const bestMove = this.minimax(this.board, 'O');
        return bestMove.index;
    }

    minimax(board, player) {
        const availableMoves = board
            .map((cell, index) => cell === '' ? index : null)
            .filter(val => val !== null);

        // Check terminal states
        if (this.checkWinCondition('X', board)) return { score: -10 };
        if (this.checkWinCondition('O', board)) return { score: 10 };
        if (availableMoves.length === 0) return { score: 0 };

        const moves = [];

        for (let i = 0; i < availableMoves.length; i++) {
            const move = {};
            move.index = availableMoves[i];
            board[availableMoves[i]] = player;

            if (player === 'O') {
                const result = this.minimax(board, 'X');
                move.score = result.score;
            } else {
                const result = this.minimax(board, 'O');
                move.score = result.score;
            }

            board[availableMoves[i]] = '';
            moves.push(move);
        }

        let bestMove;
        if (player === 'O') {
            let bestScore = -Infinity;
            for (let i = 0; i < moves.length; i++) {
                if (moves[i].score > bestScore) {
                    bestScore = moves[i].score;
                    bestMove = i;
                }
            }
        } else {
            let bestScore = Infinity;
            for (let i = 0; i < moves.length; i++) {
                if (moves[i].score < bestScore) {
                    bestScore = moves[i].score;
                    bestMove = i;
                }
            }
        }

        return moves[bestMove];
    }

    checkWinCondition(player, board = this.board) {
        return this.winningConditions.some(condition => {
            const [a, b, c] = condition;
            return board[a] === player && board[b] === player && board[c] === player;
        });
    }

    highlightWinningCells(winningCombination) {
        winningCombination.forEach(index => {
            this.boardElement.children[index].classList.add('winning');
        });
    }

    updateStatus() {
        const currentPlayerName = this.players[this.currentPlayer].name;
        this.statusElement.textContent = `${currentPlayerName}'s turn (${this.currentPlayer})`;
    }

    updateScoreboard() {
        document.getElementById('player1-score').textContent = this.players.X.score;
        document.getElementById('player2-score').textContent = this.players.O.score;
    }

    updateDifficultyIndicator() {
        const indicator = document.getElementById('difficulty-indicator');
        if (this.gameMode === 'friend') {
            indicator.style.display = 'none';
        } else {
            indicator.style.display = 'block';
            indicator.className = `difficulty-indicator difficulty-${this.difficulty}`;
            indicator.textContent = `Playing against ${this.difficulty.charAt(0).toUpperCase() + this.difficulty.slice(1)} AI`;
        }
    }

    showVictoryModal(result) {
        const modal = this.victoryModal;
        const message = document.getElementById('victory-message');
        const details = document.getElementById('victory-details');

        if (result === 'win') {
            const winnerName = this.players[this.currentPlayer].name;
            message.textContent = `🎉 ${winnerName} Wins!`;
            details.textContent = `Congratulations! You won this round!`;
        } else {
            message.textContent = `🤝 It's a Tie!`;
            details.textContent = `Great game! Nobody wins this round.`;
        }

        modal.classList.remove('hidden');
        setTimeout(() => modal.classList.add('fade-in'), 10);
    }

    closeVictoryModal() {
        this.victoryModal.classList.add('hidden');
        this.victoryModal.classList.remove('fade-in');
    }

    restartRound() {
        this.board = Array(9).fill('');
        this.currentPlayer = 'X';
        this.gameActive = true;
        this.updateStatus();
        this.boardElement.classList.remove('game-over');

        const cells = this.boardElement.querySelectorAll('.cell');
        cells.forEach(cell => {
            cell.textContent = '';
            cell.classList.remove('x', 'o', 'winning');
        });
    }

    resetScores() {
        this.players.X.score = 0;
        this.players.O.score = 0;
        this.updateScoreboard();
        this.restartRound();
    }

    backToMenu() {
        this.gameContainer.classList.add('hidden');
        this.welcomeScreen.classList.remove('hidden');
        this.welcomeScreen.classList.add('fade-in');
        this.resetScores();
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        const soundBtn = document.getElementById('sound-toggle');
        const icon = soundBtn.querySelector('i');

        if (this.soundEnabled) {
            icon.className = 'fas fa-volume-up';
            soundBtn.classList.remove('muted');
            this.sounds.click();
        } else {
            icon.className = 'fas fa-volume-mute';
            soundBtn.classList.add('muted');
        }

        this.saveSettings();
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.body.setAttribute('data-theme', this.theme);

        const themeBtn = document.getElementById('theme-toggle');
        const icon = themeBtn.querySelector('i');

        if (this.theme === 'dark') {
            icon.className = 'fas fa-sun';
            themeBtn.classList.add('theme-dark');
        } else {
            icon.className = 'fas fa-moon';
            themeBtn.classList.remove('theme-dark');
        }

        this.saveSettings();
    }

    saveSettings() {
        const settings = {
            soundEnabled: this.soundEnabled,
            theme: this.theme
        };
        localStorage.setItem('ticTacToeSettings', JSON.stringify(settings));
    }

    loadSettings() {
        const saved = localStorage.getItem('ticTacToeSettings');
        if (saved) {
            const settings = JSON.parse(saved);
            this.soundEnabled = settings.soundEnabled !== false; // Default to true
            this.theme = settings.theme || 'light';

            // Apply theme
            if (this.theme === 'dark') {
                this.toggleTheme();
            }

            // Apply sound setting
            if (!this.soundEnabled) {
                this.toggleSound();
            }
        }
    }

    // Add some fun easter eggs
    addEasterEggs() {
        // Konami code easter egg
        let konamiCode = [];
        const konamiSequence = [
            'ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown',
            'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight',
            'KeyB', 'KeyA'
        ];

        document.addEventListener('keydown', (e) => {
            konamiCode.push(e.code);
            if (konamiCode.length > konamiSequence.length) {
                konamiCode.shift();
            }

            if (konamiCode.join(',') === konamiSequence.join(',')) {
                this.activateEasterEgg();
                konamiCode = [];
            }
        });

        // Double-click title for surprise
        document.querySelector('.game-title, .welcome-title').addEventListener('dblclick', () => {
            this.surpriseAnimation();
        });
    }

    activateEasterEgg() {
        // Rainbow theme
        document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3)';
        document.body.style.backgroundSize = '400% 400%';
        document.body.style.animation = 'gradientShift 2s ease infinite';

        // Show message
        const message = document.createElement('div');
        message.textContent = '🎉 RAINBOW MODE ACTIVATED! 🌈';
        message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 15px;
            font-size: 1.5em;
            font-weight: bold;
            z-index: 10000;
            animation: bounce 1s ease-in-out;
        `;
        document.body.appendChild(message);

        setTimeout(() => {
            message.remove();
            document.body.style.background = '';
            document.body.style.animation = '';
        }, 3000);
    }

    surpriseAnimation() {
        const title = document.querySelector('.game-title, .welcome-title');
        title.style.animation = 'none';
        setTimeout(() => {
            title.style.animation = 'bounce 0.5s ease-in-out 3';
        }, 10);
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new UltimateTicTacToe();
    game.addEasterEggs();

    // Add some helpful tips
    console.log('🎮 Welcome to Ultimate Tic Tac Toe!');
    console.log('💡 Tips:');
    console.log('   - Try the Konami code for a surprise!');
    console.log('   - Double-click the title for fun!');
    console.log('   - Hard AI is unbeatable - can you tie?');
    console.log('   - Toggle sound and themes in the game!');
});
