class TicTacToe {
    constructor() {
        this.board = Array(9).fill('');
        this.currentPlayer = 'X';
        this.gameActive = true;
        this.statusElement = document.getElementById('status');
        this.boardElement = document.getElementById('game-board');
        this.restartButton = document.getElementById('restart-btn');
        
        this.winningConditions = [
            [0, 1, 2],
            [3, 4, 5],
            [6, 7, 8],
            [0, 3, 6],
            [1, 4, 7],
            [2, 5, 8],
            [0, 4, 8],
            [2, 4, 6]
        ];
        
        this.initializeGame();
    }
    
    initializeGame() {
        this.createBoard();
        this.updateStatus(`Player ${this.currentPlayer}'s turn`);
        this.restartButton.addEventListener('click', () => this.restartGame());
    }
    
    createBoard() {
        const cells = this.boardElement.querySelectorAll('.cell');
        cells.forEach((cell, index) => {
            cell.addEventListener('click', () => this.handleCellClick(index));
        });
    }
    
    handleCellClick(index) {
        if (this.board[index] !== '' || !this.gameActive) {
            return;
        }
        
        this.makeMove(index);
        this.checkResult();
    }
    
    makeMove(index) {
        this.board[index] = this.currentPlayer;
        const cell = this.boardElement.children[index];
        cell.textContent = this.currentPlayer;
        cell.classList.add(this.currentPlayer.toLowerCase());
    }
    
    checkResult() {
        let roundWon = false;
        let winningCombination = null;
        
        for (let i = 0; i < this.winningConditions.length; i++) {
            const [a, b, c] = this.winningConditions[i];
            if (this.board[a] && this.board[a] === this.board[b] && this.board[a] === this.board[c]) {
                roundWon = true;
                winningCombination = this.winningConditions[i];
                break;
            }
        }
        
        if (roundWon) {
            this.updateStatus(`Player ${this.currentPlayer} wins!`);
            this.gameActive = false;
            this.highlightWinningCells(winningCombination);
            this.boardElement.classList.add('game-over');
            return;
        }
        
        if (!this.board.includes('')) {
            this.updateStatus("It's a tie!");
            this.gameActive = false;
            this.boardElement.classList.add('game-over');
            return;
        }
        
        this.switchPlayer();
    }
    
    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 'X' ? 'O' : 'X';
        this.updateStatus(`Player ${this.currentPlayer}'s turn`);
    }
    
    highlightWinningCells(winningCombination) {
        winningCombination.forEach(index => {
            this.boardElement.children[index].classList.add('winning');
        });
    }
    
    updateStatus(message) {
        this.statusElement.textContent = message;
    }
    
    restartGame() {
        this.board = Array(9).fill('');
        this.currentPlayer = 'X';
        this.gameActive = true;
        this.updateStatus(`Player ${this.currentPlayer}'s turn`);
        this.boardElement.classList.remove('game-over');
        
        const cells = this.boardElement.querySelectorAll('.cell');
        cells.forEach(cell => {
            cell.textContent = '';
            cell.classList.remove('x', 'o', 'winning');
        });
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TicTacToe();
});
