<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sui Explorer - Mainnet | Home</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔗</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <div class="logo">
                    <span class="logo-icon">🔗</span>
                    <span class="logo-text">Sui Explorer</span>
                    <span class="network-badge">Mainnet</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search by Address / Txn Hash / Block">
                    <button class="search-btn">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <div class="header-right">
                <button class="network-selector">
                    <span>Mainnet</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-container">
            <a href="#" class="nav-item active">
                <i class="fas fa-home"></i>
                Home
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-cube"></i>
                Transactions
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-layer-group"></i>
                Objects
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-coins"></i>
                Coins
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-chart-line"></i>
                Analytics
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">24,567,891</div>
                        <div class="stat-label">Total Transactions</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">1,234,567</div>
                        <div class="stat-label">Total Objects</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-gas-pump"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">892 MIST</div>
                        <div class="stat-label">Reference Gas Price</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">2.8s</div>
                        <div class="stat-label">Avg Block Time</div>
                    </div>
                </div>
            </div>

            <!-- Latest Transaction Blocks -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">Latest Transaction Blocks</h2>
                    <a href="#" class="view-all-link">View all</a>
                </div>

                <div class="table-container">
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Type / Func
                                </th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Digest
                                </th>
                                <th>Age</th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Sender
                                </th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Transactions
                                </th>
                                <th>Gas</th>
                            </tr>
                        </thead>
                        <tbody id="transactions-tbody">
                            <!-- Transactions will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Validators Section -->
            <div class="section validators-section">
                <div class="section-header">
                    <h2 class="section-title">Validators</h2>
                    <a href="#" class="view-all-link">View all</a>
                </div>

                <div class="table-container">
                    <table class="validators-table">
                        <thead>
                            <tr>
                                <th>Validator</th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Stake
                                </th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Next Epoch Stake
                                </th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Current Epoch Gas Price
                                </th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    Next Epoch Gas Price
                                </th>
                                <th class="sortable">
                                    <i class="fas fa-sort"></i>
                                    APY
                                </th>
                            </tr>
                        </thead>
                        <tbody id="validators-tbody">
                            <!-- Validators will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">Loading transactions...</div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
