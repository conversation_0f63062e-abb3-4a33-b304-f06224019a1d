class BlockchainExplorer {
    constructor() {
        this.transactions = [];
        this.validators = [];
        this.isLoading = false;
        this.sortColumn = null;
        this.sortDirection = 'desc';

        this.initializeApp();
        this.generateMockData();
        this.generateValidatorsData();
        this.startRealTimeUpdates();
    }
    
    initializeApp() {
        this.setupEventListeners();
        this.hideLoading();
    }
    
    setupEventListeners() {
        // Search functionality
        const searchInput = document.querySelector('.search-input');
        const searchBtn = document.querySelector('.search-btn');
        
        searchBtn.addEventListener('click', () => this.handleSearch());
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleSearch();
        });
        
        // Table sorting
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => this.handleSort(header));
        });
        
        // Network selector
        const networkSelector = document.querySelector('.network-selector');
        networkSelector.addEventListener('click', () => this.showNetworkOptions());
        
        // Navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(item);
            });
        });
    }
    
    generateMockData() {
        const txTypes = [
            { type: 'Programmable Tx', func: 'claim_stamp', badge: 'Programmable' },
            { type: 'Programmable Tx', func: 'batch_trade_with_provided_gas_fee', badge: 'Programmable' },
            { type: 'Programmable Tx', func: 'generate_proof_as_trader', badge: 'Programmable' },
            { type: 'Programmable Tx', func: 'zero', badge: 'Programmable' },
            { type: 'ConsensusCommitPrologueV4', func: '', badge: 'Consensus' }
        ];
        
        const senders = [
            '0x000000000000000000000000000000000000000000000000000000000000000000000000',
            '0xee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4',
            '0x2a2195c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4',
            '0x1a66b9c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4',
            '0xd92107c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4ee6ed2c4'
        ];
        
        for (let i = 0; i < 20; i++) {
            const txType = txTypes[Math.floor(Math.random() * txTypes.length)];
            const sender = senders[Math.floor(Math.random() * senders.length)];
            const isSystemAccount = sender === senders[0];
            
            this.transactions.push({
                id: this.generateHash(),
                type: txType.type,
                func: txType.func,
                badge: txType.badge,
                digest: this.generateHash(),
                age: this.generateAge(),
                sender: sender,
                senderDisplay: isSystemAccount ? 'Sui System account' : this.truncateAddress(sender),
                transactions: Math.floor(Math.random() * 5),
                gas: this.generateGas(),
                status: Math.random() > 0.1 ? 'success' : 'pending',
                timestamp: Date.now() - Math.random() * 3600000
            });
        }
        
        this.renderTransactions();
    }

    generateValidatorsData() {
        const validatorNames = [
            { name: 'Stakelab x...', icon: '🔵', color: 'blue', verified: true },
            { name: 'OKXEarn', icon: '⚫', color: 'black', verified: false },
            { name: 'MIDL.dev', icon: '🟠', color: 'orange', verified: false },
            { name: 'Chainbase', icon: '⚫', color: 'black', verified: true },
            { name: 'P2P.ORG', icon: '🔵', color: 'blue', verified: true },
            { name: 'Republic Crypto |...', icon: '🟢', color: 'green', verified: false },
            { name: 'proofgroup', icon: '🔴', color: 'red', verified: false },
            { name: 'HashedPotatoes', icon: '⚪', color: 'gray', verified: false }
        ];

        validatorNames.forEach((validator, index) => {
            const baseStake = 100000000 + Math.random() * 50000000;
            const currentGasPrice = [330, 1000, 758, 450, 450, 258, 406, 200][index] || Math.floor(Math.random() * 1000) + 200;
            const nextGasPrice = currentGasPrice + Math.floor(Math.random() * 100) - 50;

            this.validators.push({
                id: index,
                name: validator.name,
                icon: validator.icon,
                color: validator.color,
                verified: validator.verified,
                address: this.generateValidatorAddress(),
                stake: {
                    current: Math.floor(baseStake),
                    next: Math.floor(baseStake + (Math.random() * 1000000) - 500000)
                },
                gasPrice: {
                    current: currentGasPrice,
                    next: nextGasPrice
                },
                apy: (2 + Math.random() * 0.5).toFixed(1)
            });
        });

        this.renderValidators();
    }

    generateValidatorAddress() {
        const chars = '0123456789abcdef';
        let address = '0x';
        for (let i = 0; i < 40; i++) {
            address += chars[Math.floor(Math.random() * chars.length)];
        }
        return address;
    }

    renderValidators() {
        const tbody = document.getElementById('validators-tbody');
        tbody.innerHTML = '';

        this.validators.forEach(validator => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="validator-info">
                        <div class="validator-avatar ${validator.color}">
                            ${validator.icon}
                        </div>
                        <div class="validator-details">
                            <div class="validator-name">
                                ${validator.name}
                                ${validator.verified ? '<span class="validator-badge">NEW</span>' : ''}
                            </div>
                            <a href="#" class="validator-address">${this.truncateAddress(validator.address)}</a>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="stake-amount">${validator.stake.current.toLocaleString()}</span>
                    <span class="stake-unit">SUI</span>
                </td>
                <td>
                    <span class="stake-amount">${validator.stake.next.toLocaleString()}</span>
                    <span class="stake-unit">SUI</span>
                </td>
                <td>
                    <span class="gas-price">${validator.gasPrice.current}</span>
                    <span class="gas-price-unit">MIST</span>
                </td>
                <td>
                    <span class="gas-price">${validator.gasPrice.next}</span>
                    <span class="gas-price-unit">MIST</span>
                </td>
                <td>
                    <span class="apy-value">${validator.apy}%</span>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    generateHash() {
        const chars = '0123456789abcdef';
        let hash = '0x';
        for (let i = 0; i < 64; i++) {
            hash += chars[Math.floor(Math.random() * chars.length)];
        }
        return hash;
    }
    
    generateAge() {
        const seconds = Math.floor(Math.random() * 300) + 1;
        return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    }
    
    generateGas() {
        const amount = Math.floor(Math.random() * 10000000) + 500000;
        return {
            amount: amount.toLocaleString(),
            unit: Math.random() > 0.5 ? 'SUI' : 'MIST'
        };
    }
    
    truncateAddress(address) {
        return `${address.slice(0, 6)}...${address.slice(-6)}`;
    }
    
    renderTransactions() {
        const tbody = document.getElementById('transactions-tbody');
        tbody.innerHTML = '';
        
        this.transactions.forEach(tx => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="tx-type">
                        <span class="tx-badge">${tx.badge}</span>
                        ${tx.func ? `<span class="tx-function">${tx.func}</span>` : ''}
                    </div>
                </td>
                <td>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span class="status-${tx.status}">●</span>
                        <a href="#" class="digest-link">${this.truncateAddress(tx.digest)}</a>
                    </div>
                </td>
                <td class="age">${tx.age}</td>
                <td>
                    ${tx.senderDisplay === 'Sui System account' 
                        ? `<span style="color: #f59e0b;">📋 ${tx.senderDisplay}</span>`
                        : `<a href="#" class="address">${tx.senderDisplay}</a>`
                    }
                </td>
                <td style="text-align: center;">${tx.transactions}</td>
                <td>
                    <div>
                        <span class="gas-amount">${tx.gas.amount}</span>
                        <span class="gas-unit">${tx.gas.unit}</span>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    handleSearch() {
        const searchInput = document.querySelector('.search-input');
        const query = searchInput.value.trim();
        
        if (!query) return;
        
        this.showLoading();
        
        // Simulate search delay
        setTimeout(() => {
            this.hideLoading();
            this.showSearchResults(query);
        }, 1500);
    }
    
    showSearchResults(query) {
        alert(`Searching for: ${query}\n\nThis is a demo - in a real explorer, this would search the blockchain for transactions, addresses, or blocks matching your query.`);
    }
    
    handleSort(header) {
        const column = header.textContent.trim().toLowerCase();
        const isValidatorsTable = header.closest('.validators-table') !== null;

        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'desc';
        }

        // Update header icons in the current table
        const currentTable = header.closest('table');
        currentTable.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        const icon = header.querySelector('i');
        icon.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'}`;

        if (isValidatorsTable) {
            this.sortValidators();
        } else {
            this.sortTransactions();
        }
    }
    
    sortTransactions() {
        this.transactions.sort((a, b) => {
            let aVal, bVal;
            
            switch (this.sortColumn) {
                case 'type / func':
                    aVal = a.type + a.func;
                    bVal = b.type + b.func;
                    break;
                case 'digest':
                    aVal = a.digest;
                    bVal = b.digest;
                    break;
                case 'sender':
                    aVal = a.sender;
                    bVal = b.sender;
                    break;
                case 'transactions':
                    aVal = a.transactions;
                    bVal = b.transactions;
                    break;
                default:
                    return 0;
            }
            
            if (typeof aVal === 'string') {
                return this.sortDirection === 'asc' 
                    ? aVal.localeCompare(bVal)
                    : bVal.localeCompare(aVal);
            } else {
                return this.sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
            }
        });
        
        this.renderTransactions();
    }

    sortValidators() {
        this.validators.sort((a, b) => {
            let aVal, bVal;

            switch (this.sortColumn) {
                case 'stake':
                    aVal = a.stake.current;
                    bVal = b.stake.current;
                    break;
                case 'next epoch stake':
                    aVal = a.stake.next;
                    bVal = b.stake.next;
                    break;
                case 'current epoch gas price':
                    aVal = a.gasPrice.current;
                    bVal = b.gasPrice.current;
                    break;
                case 'next epoch gas price':
                    aVal = a.gasPrice.next;
                    bVal = b.gasPrice.next;
                    break;
                case 'apy':
                    aVal = parseFloat(a.apy);
                    bVal = parseFloat(b.apy);
                    break;
                default:
                    return 0;
            }

            return this.sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
        });

        this.renderValidators();
    }
    
    handleNavigation(item) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
        
        // Add active class to clicked item
        item.classList.add('active');
        
        const page = item.textContent.trim();
        this.showNotification(`Navigating to ${page} page...`);
    }
    
    showNetworkOptions() {
        const networks = ['Mainnet', 'Testnet', 'Devnet'];
        const current = 'Mainnet';
        
        const options = networks.map(net => 
            `${net === current ? '✓ ' : '  '}${net}`
        ).join('\n');
        
        alert(`Select Network:\n\n${options}\n\nCurrently on: ${current}`);
    }
    
    startRealTimeUpdates() {
        // Simulate real-time transaction updates
        setInterval(() => {
            if (Math.random() > 0.7) { // 30% chance every 5 seconds
                this.addNewTransaction();
            }
            this.updateAges();
        }, 5000);

        // Update validator data every 30 seconds
        setInterval(() => {
            this.updateValidatorData();
        }, 30000);
    }

    updateValidatorData() {
        this.validators.forEach(validator => {
            // Slightly adjust stake amounts
            const stakeChange = (Math.random() - 0.5) * 1000000;
            validator.stake.current = Math.max(50000000, validator.stake.current + stakeChange);
            validator.stake.next = Math.max(50000000, validator.stake.next + stakeChange);

            // Slightly adjust gas prices
            const gasPriceChange = Math.floor((Math.random() - 0.5) * 50);
            validator.gasPrice.current = Math.max(100, validator.gasPrice.current + gasPriceChange);
            validator.gasPrice.next = Math.max(100, validator.gasPrice.next + gasPriceChange);

            // Slightly adjust APY
            const apyChange = (Math.random() - 0.5) * 0.2;
            validator.apy = Math.max(1.5, Math.min(3.0, parseFloat(validator.apy) + apyChange)).toFixed(1);
        });

        this.renderValidators();
        this.showNotification('Validator data updated!');
    }
    
    addNewTransaction() {
        const txTypes = [
            { type: 'Programmable Tx', func: 'swap_exact_input', badge: 'Programmable' },
            { type: 'Programmable Tx', func: 'mint_nft', badge: 'Programmable' },
            { type: 'ConsensusCommitPrologueV4', func: '', badge: 'Consensus' }
        ];
        
        const txType = txTypes[Math.floor(Math.random() * txTypes.length)];
        const newTx = {
            id: this.generateHash(),
            type: txType.type,
            func: txType.func,
            badge: txType.badge,
            digest: this.generateHash(),
            age: '0m 1s',
            sender: this.generateHash(),
            senderDisplay: this.truncateAddress(this.generateHash()),
            transactions: Math.floor(Math.random() * 3) + 1,
            gas: this.generateGas(),
            status: 'success',
            timestamp: Date.now()
        };
        
        this.transactions.unshift(newTx);
        this.transactions = this.transactions.slice(0, 20); // Keep only latest 20
        this.renderTransactions();
        
        this.showNotification('New transaction detected!');
    }
    
    updateAges() {
        this.transactions.forEach(tx => {
            const ageInSeconds = Math.floor((Date.now() - tx.timestamp) / 1000);
            const minutes = Math.floor(ageInSeconds / 60);
            const seconds = ageInSeconds % 60;
            tx.age = `${minutes}m ${seconds}s`;
        });
        
        this.renderTransactions();
    }
    
    showLoading() {
        document.getElementById('loading-overlay').classList.remove('hidden');
    }
    
    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }
    
    showNotification(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new BlockchainExplorer();
});
