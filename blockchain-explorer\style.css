/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #fafafa;
    color: #1a1a1a;
    line-height: 1.5;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 18px;
}

.logo-icon {
    font-size: 24px;
}

.network-badge {
    background: #10b981;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.header-center {
    flex: 1;
    max-width: 600px;
    margin: 0 40px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0 12px;
    transition: all 0.2s;
}

.search-container:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    color: #6b7280;
    margin-right: 8px;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 0;
    font-size: 14px;
    outline: none;
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
}

.search-btn:hover {
    background: #2563eb;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.network-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.network-selector:hover {
    background: #f3f4f6;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
}

/* Navigation */
.nav {
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    gap: 32px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 0;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.nav-item:hover {
    color: #374151;
}

.nav-item.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

/* Main Content */
.main {
    padding: 32px 0;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s;
}

.stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: #eff6ff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    font-size: 20px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

/* Section */
.section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
}

.view-all-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.2s;
}

.view-all-link:hover {
    color: #2563eb;
}

/* Table */
.table-container {
    overflow-x: auto;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.transactions-table th {
    background: #f9fafb;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e5e7eb;
}

.transactions-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background 0.2s;
}

.transactions-table th.sortable:hover {
    background: #f3f4f6;
}

.transactions-table th i {
    margin-right: 4px;
    opacity: 0.5;
}

.transactions-table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    font-size: 14px;
    vertical-align: middle;
}

.transactions-table tr:hover {
    background: #fafbfc;
}

/* Transaction specific styles */
.tx-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tx-badge {
    background: #eff6ff;
    color: #3b82f6;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.tx-function {
    color: #10b981;
    font-size: 12px;
    font-weight: 500;
}

.digest-link {
    color: #3b82f6;
    text-decoration: none;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
}

.digest-link:hover {
    text-decoration: underline;
}

.status-success {
    color: #10b981;
}

.status-pending {
    color: #f59e0b;
}

.age {
    color: #6b7280;
    font-size: 13px;
}

.address {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    color: #3b82f6;
    text-decoration: none;
}

.address:hover {
    text-decoration: underline;
}

.gas-amount {
    font-weight: 600;
    color: #1a1a1a;
}

.gas-unit {
    color: #6b7280;
    font-size: 12px;
}

/* Validators Section */
.validators-section {
    margin-top: 32px;
}

.validators-table {
    width: 100%;
    border-collapse: collapse;
}

.validators-table th {
    background: #f9fafb;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e5e7eb;
}

.validators-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background 0.2s;
}

.validators-table th.sortable:hover {
    background: #f3f4f6;
}

.validators-table th i {
    margin-right: 4px;
    opacity: 0.5;
}

.validators-table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    font-size: 14px;
    vertical-align: middle;
}

.validators-table tr:hover {
    background: #fafbfc;
}

/* Validator specific styles */
.validator-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.validator-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.validator-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.validator-name {
    font-weight: 600;
    color: #1a1a1a;
    font-size: 14px;
}

.validator-address {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 11px;
    color: #3b82f6;
    text-decoration: none;
}

.validator-address:hover {
    text-decoration: underline;
}

.validator-badge {
    background: #3b82f6;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 8px;
}

.stake-amount {
    font-weight: 600;
    color: #1a1a1a;
}

.stake-unit {
    color: #6b7280;
    font-size: 12px;
    margin-left: 4px;
}

.gas-price {
    font-weight: 500;
    color: #1a1a1a;
}

.gas-price-unit {
    color: #6b7280;
    font-size: 12px;
    margin-left: 4px;
}

.apy-value {
    font-weight: 600;
    color: #10b981;
}

/* Validator avatar colors */
.validator-avatar.blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.validator-avatar.purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.validator-avatar.orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.validator-avatar.black {
    background: linear-gradient(135deg, #374151, #1f2937);
    color: white;
}

.validator-avatar.green {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.validator-avatar.red {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.validator-avatar.gray {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

/* Bottom Sections */
.bottom-sections {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 32px;
    margin-top: 48px;
    padding-bottom: 48px;
}

.info-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.info-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    background: #fafbfc;
}

.info-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

.info-title i {
    color: #3b82f6;
    font-size: 14px;
}

.more-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.more-link:hover {
    text-decoration: underline;
}

.info-content {
    padding: 24px;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    margin-bottom: 16px;
}

.info-list li:last-child {
    margin-bottom: 0;
}

.info-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    padding: 8px 0;
    transition: color 0.2s;
}

.info-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.info-link::before {
    content: '›';
    margin-right: 8px;
    color: #9ca3af;
    font-weight: bold;
}

/* Directory Items */
.directory-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.directory-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.directory-item:last-child {
    border-bottom: none;
}

.directory-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.directory-icon.ant {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.directory-icon.artipedia {
    background: linear-gradient(135deg, #1f2937, #374151);
}

.directory-icon.rango {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.directory-icon.cctoo {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.directory-icon.community {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

.directory-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.directory-name {
    font-weight: 600;
    color: #1a1a1a;
    font-size: 14px;
}

.directory-category {
    font-size: 12px;
    color: #10b981;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .bottom-sections {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}

@media (max-width: 768px) {
    .bottom-sections {
        margin-top: 32px;
        padding-bottom: 32px;
    }

    .info-header {
        padding: 16px 20px;
    }

    .info-content {
        padding: 20px;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6b7280;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 0 16px;
        flex-direction: column;
        height: auto;
        gap: 16px;
        padding-top: 16px;
        padding-bottom: 16px;
    }
    
    .header-center {
        margin: 0;
        width: 100%;
    }
    
    .nav-container {
        padding: 0 16px;
        overflow-x: auto;
    }
    
    .container {
        padding: 0 16px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .transactions-table {
        font-size: 12px;
    }
    
    .transactions-table th,
    .transactions-table td {
        padding: 12px 8px;
    }
}
