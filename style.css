/* CSS Variables for Theming */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --light-color: #ffffff;
    --dark-color: #2c3e50;
    --text-color: #333;
    --bg-color: #f8f9fa;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] {
    --primary-color: #bb86fc;
    --secondary-color: #3700b3;
    --accent-color: #cf6679;
    --light-color: #1e1e1e;
    --dark-color: #ffffff;
    --text-color: #ffffff;
    --bg-color: #121212;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-color);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color);
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -1;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Welcome Screen */
.welcome-screen {
    text-align: center;
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--shadow);
    max-width: 500px;
    width: 90%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-title {
    font-family: 'Fredoka One', cursive;
    font-size: 3em;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.welcome-subtitle {
    font-size: 1.2em;
    color: var(--text-color);
    margin-bottom: 30px;
    opacity: 0.8;
}

.game-modes {
    margin-bottom: 30px;
}

.game-modes h3 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-weight: 600;
}

.mode-btn {
    display: block;
    width: 100%;
    margin: 10px 0;
    padding: 15px 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.mode-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.mode-btn.selected {
    background: linear-gradient(45deg, var(--success-color), #27ae60);
    animation: pulse 1s infinite;
}

.mode-btn i {
    margin-right: 10px;
    font-size: 1.2em;
}

.player-setup {
    margin-bottom: 30px;
}

.player-input {
    margin: 15px 0;
    text-align: left;
}

.player-input label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--text-color);
}

.player-input input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1em;
    transition: var(--transition);
    background: var(--light-color);
    color: var(--text-color);
}

.player-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.start-btn {
    background: linear-gradient(45deg, var(--success-color), #27ae60);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: var(--border-radius);
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.start-btn i {
    margin-right: 10px;
}

/* Main Game Container */
.container {
    text-align: center;
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
    max-width: 500px;
    width: 90%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.game-title {
    font-family: 'Fredoka One', cursive;
    font-size: 2em;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    transform: scale(1.1);
    background: var(--secondary-color);
}

/* Scoreboard */
.scoreboard {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.player-score {
    text-align: center;
    flex: 1;
}

.player-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
}

.player-name {
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 5px;
}

.player-symbol {
    font-size: 1.5em;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 50%;
    color: white;
}

.x-symbol {
    background: var(--danger-color);
}

.o-symbol {
    background: var(--info-color);
}

.score {
    font-size: 2em;
    font-weight: bold;
    color: var(--primary-color);
}

.vs {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--text-color);
    margin: 0 20px;
}

/* Game Info */
.game-info {
    margin-bottom: 20px;
}

.status {
    font-size: 1.3em;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
    padding: 10px;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.game-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.game-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.game-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Game Board */
.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 8px;
    background: var(--dark-color);
    border-radius: var(--border-radius);
    padding: 8px;
    margin: 0 auto 20px;
    width: 320px;
    height: 320px;
    box-shadow: var(--shadow);
}

.cell {
    background: var(--light-color);
    border: none;
    font-size: 2.5em;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.cell::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.cell:hover::before {
    left: 100%;
}

.cell:hover {
    background: #f8f9fa;
    transform: scale(0.95);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.cell.x {
    color: var(--danger-color);
    animation: popIn 0.3s ease-out;
}

.cell.o {
    color: var(--info-color);
    animation: popIn 0.3s ease-out;
}

@keyframes popIn {
    0% { transform: scale(0); }
    80% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.cell.winning {
    background: var(--success-color) !important;
    color: white !important;
    animation: winningPulse 0.8s ease-in-out infinite;
}

@keyframes winningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px var(--success-color); }
}

.game-over {
    pointer-events: none;
}

.game-over .cell:not(.winning) {
    opacity: 0.6;
    filter: grayscale(50%);
}

/* Difficulty Indicator */
.difficulty-indicator {
    text-align: center;
    padding: 10px;
    border-radius: var(--border-radius);
    font-weight: 600;
    margin-top: 10px;
}

.difficulty-easy {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.difficulty-medium {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.difficulty-hard {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

/* Victory Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.victory-animation {
    font-size: 4em;
    margin-bottom: 20px;
    animation: celebrate 1s ease-in-out infinite;
}

@keyframes celebrate {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(-10deg) scale(1.1); }
    75% { transform: rotate(10deg) scale(1.1); }
}

.modal h2 {
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 2em;
}

.modal p {
    color: var(--text-color);
    margin-bottom: 30px;
    opacity: 0.8;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.modal-btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-btn.primary {
    background: var(--success-color);
    color: white;
}

.modal-btn:not(.primary) {
    background: var(--bg-color);
    color: var(--text-color);
    border: 2px solid var(--primary-color);
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-screen, .container {
        margin: 20px;
        padding: 20px;
    }

    .welcome-title {
        font-size: 2.5em;
    }

    .game-title {
        font-size: 1.5em;
    }

    .game-board {
        width: 280px;
        height: 280px;
    }

    .cell {
        font-size: 2em;
    }

    .scoreboard {
        flex-direction: column;
        gap: 15px;
    }

    .vs {
        margin: 0;
    }

    .game-controls {
        flex-direction: column;
        align-items: center;
    }

    .modal-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .game-board {
        width: 240px;
        height: 240px;
    }

    .cell {
        font-size: 1.8em;
    }

    .welcome-title {
        font-size: 2em;
    }
}

/* Sound and Theme Toggle States */
.control-btn.muted {
    background: var(--danger-color);
}

.control-btn.theme-dark {
    background: var(--dark-color);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pulse Animation for Important Elements */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Smooth Transitions for Theme Changes */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
