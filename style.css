* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    text-align: center;
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 100%;
}

h1 {
    color: #333;
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    margin-bottom: 20px;
}

#status {
    font-size: 1.2em;
    font-weight: bold;
    color: #555;
    margin-bottom: 15px;
}

#restart-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
}

#restart-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 5px;
    background: #333;
    border-radius: 10px;
    padding: 5px;
    margin: 0 auto;
    width: 300px;
    height: 300px;
}

.cell {
    background: white;
    border: none;
    font-size: 2em;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    border-radius: 5px;
}

.cell:hover {
    background: #f0f0f0;
    transform: scale(0.95);
}

.cell.x {
    color: #e74c3c;
}

.cell.o {
    color: #3498db;
}

.cell.winning {
    background: #2ecc71 !important;
    color: white !important;
    animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.game-over {
    pointer-events: none;
}

.game-over .cell:not(.winning) {
    opacity: 0.5;
}
