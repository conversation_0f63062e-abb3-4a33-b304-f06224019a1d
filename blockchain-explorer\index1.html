<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sui Explorer - Educational Replica</title>
    <meta name="description" content="Educational replica of a blockchain explorer interface">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <div class="explorer-header">
            <div class="container">
                <div class="header-wrapper">
                    <div class="header-info">
                        <div class="market-params">
                            <div class="market-param">
                                <span class="title">Sui:</span>
                                <span class="value-icon">$</span>
                                <span class="value">3.92</span>
                                <div class="change-value negative">-8.07%</div>
                            </div>
                            <div class="market-param">
                                <span class="title">Av. Gas:</span>
                                <span class="value-icon">⛽</span>
                                <span class="value">0.001905738</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-panel">
                        <div class="connect-btn">
                            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5 7.33333C5 6.59694 5.63491 6 6.41818 6H17.0545C17.4461 6 17.7636 6.29848 17.7636 6.66667C17.7636 7.03485 17.4461 7.33333 17.0545 7.33333H7.12727C6.73565 7.33333 6.41818 7.63181 6.41818 8C6.41818 8.36819 6.71666 8.66667 7.08485 8.66667H17.7636C18.5469 8.66667 19.1818 9.26362 19.1818 10V16.6667C19.1818 17.403 18.5469 18 17.7636 18H6.41818C5.63494 18 5 17.403 5 16.6667V7.33333ZM14.9267 13.3333C14.9267 12.597 15.5236 12 16.26 12H19.1812V14.6667H16.26C15.5236 14.6667 14.9267 14.0697 14.9267 13.3333ZM17.0546 14.0004C17.4463 14.0004 17.7637 13.7019 17.7637 13.3337C17.7637 12.9655 17.4463 12.6671 17.0546 12.6671C16.663 12.6671 16.3456 12.9655 16.3456 13.3337C16.3456 13.7019 16.663 14.0004 17.0546 14.0004Z" fill="currentColor"/>
                            </svg>
                            <p>Connect</p>
                        </div>
                        <div class="network-switcher">
                            <svg width="22" height="13" viewBox="0 0 22 13" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9969 2.80039C15.2425 2.80039 18.7346 6.02706 19.1546 10.1618C19.2065 10.6735 19.62 11.1004 20.1469 11.1004C20.6692 11.1004 21.1057 10.674 21.061 10.1423C20.6254 4.96582 16.286 0.900391 10.9969 0.900391C5.70783 0.900391 1.36839 4.96582 0.932847 10.1423C0.888116 10.674 1.32461 11.1004 1.84691 11.1004C2.37386 11.1004 2.78728 10.6735 2.83925 10.1618C3.25917 6.02706 6.75129 2.80039 10.9969 2.80039Z" fill="#10B981"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9969 4.90039C7.92037 4.90039 5.37594 7.17785 4.95729 10.1388C4.88171 10.6734 5.32669 11.1004 5.84691 11.1004C6.37535 11.1004 6.77929 10.6703 6.87792 10.1752C7.26128 8.25073 8.95983 6.80039 10.9969 6.80039C13.034 6.80039 14.7325 8.25073 15.1159 10.1752C15.2145 10.6703 15.6185 11.1004 16.1469 11.1004C16.6671 11.1004 17.1121 10.6734 17.0365 10.1388C16.6179 7.17786 14.0734 4.90039 10.9969 4.90039Z" fill="#10B981"/>
                                <path d="M12.9971 11.0005C12.9971 12.1051 12.1016 13.0005 10.9971 13.0005C9.8925 13.0005 8.99707 12.1051 8.99707 11.0005C8.99707 9.89592 9.8925 9.00049 10.9971 9.00049C12.1016 9.00049 12.9971 9.89592 12.9971 11.0005Z" fill="#10B981"/>
                            </svg>
                            <span>Mainnet</span>
                        </div>
                        <div class="blockchain-switcher">
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_59259_1053)">
                                    <path d="M3.75 12.75C4.57841 12.75 5.25 13.4216 5.25 14.25C5.25 15.0784 4.57841 15.75 3.75 15.75C2.92157 15.75 2.25 15.0784 2.25 14.25C2.25 13.4216 2.92157 12.75 3.75 12.75ZM9 12.75C9.82841 12.75 10.5 13.4216 10.5 14.25C10.5 15.0784 9.82841 15.75 9 15.75C8.17159 15.75 7.5 15.0784 7.5 14.25C7.5 13.4216 8.17159 12.75 9 12.75ZM14.25 12.75C15.0784 12.75 15.75 13.4216 15.75 14.25C15.75 15.0784 15.0784 15.75 14.25 15.75C13.4216 15.75 12.75 15.0784 12.75 14.25C12.75 13.4216 13.4216 12.75 14.25 12.75ZM3.75 7.5C4.57841 7.5 5.25 8.17159 5.25 9C5.25 9.82841 4.57841 10.5 3.75 10.5C2.92157 10.5 2.25 9.82841 2.25 9C2.25 8.17159 2.92157 7.5 3.75 7.5ZM9 7.5C9.82841 7.5 10.5 8.17159 10.5 9C10.5 9.82841 9.82841 10.5 9 10.5C8.17159 10.5 7.5 9.82841 7.5 9C7.5 8.17159 8.17159 7.5 9 7.5ZM14.25 7.5C15.0784 7.5 15.75 8.17159 15.75 9C15.75 9.82841 15.0784 10.5 14.25 10.5C13.4216 10.5 12.75 9.82841 12.75 9C12.75 8.17159 13.4216 7.5 14.25 7.5ZM3.75 2.25C4.57841 2.25 5.25 2.92157 5.25 3.75C5.25 4.57841 4.57841 5.25 3.75 5.25C2.92157 5.25 2.25 4.57841 2.25 3.75C2.25 2.92157 2.92157 2.25 3.75 2.25ZM9 2.25C9.82841 2.25 10.5 2.92157 10.5 3.75C10.5 4.57841 9.82841 5.25 9 5.25C8.17159 5.25 7.5 4.57841 7.5 3.75C7.5 2.92157 8.17159 2.25 9 2.25ZM14.25 2.25C15.0784 2.25 15.75 2.92157 15.75 3.75C15.75 4.57841 15.0784 5.25 14.25 5.25C13.4216 5.25 12.75 4.57841 12.75 3.75C12.75 2.92157 13.4216 2.25 14.25 2.25Z" fill="var(--text-main)"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_59259_1053">
                                        <rect width="18" height="18" fill="white"/>
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="active-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="project-header">
        <div class="navigation">
            <div class="container">
                <div class="nav-wrapper">
                    <div class="logo">
                        <a href="#"><svg width="149" height="32" viewBox="0 0 149 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 13.6533C0 8.87422 0 6.48466 0.930078 4.65928C1.7482 3.05363 3.05363 1.7482 4.65928 0.930078C6.48466 0 8.87422 0 13.6533 0H18.3467C23.1258 0 25.5153 0 27.3407 0.930078C28.9464 1.7482 30.2518 3.05363 31.0699 4.65928C32 6.48466 32 8.87422 32 13.6533V18.3467C32 23.1258 32 25.5153 31.0699 27.3407C30.2518 28.9464 28.9464 30.2518 27.3407 31.0699C25.5153 32 23.1258 32 18.3467 32H13.6533C8.87422 32 6.48466 32 4.65928 31.0699C3.05363 30.2518 1.7482 28.9464 0.930078 27.3407C0 25.5153 0 23.1258 0 18.3467V13.6533Z" fill="#4C72FF"/>
<path d="M24.2596 11.8904C24.4991 11.7426 24.7881 11.708 25.0135 11.7996C25.1261 11.8349 25.2177 11.912 25.2811 12.0105C25.3375 12.1092 25.3723 12.222 25.3582 12.3347L24.8797 20.2713C24.8232 21.3001 24.4426 22.3432 23.8084 23.3015C21.9971 26.0431 18.2684 27.3538 15.4774 26.2263C14.688 25.9092 14.0603 25.4295 13.6092 24.8445C13.7149 24.8516 13.8209 24.8523 13.9266 24.8523C16.7952 24.8522 19.6775 23.2939 21.2703 20.8836C22.0456 19.7066 22.4975 18.4242 22.575 17.1697L22.8358 12.7576L24.2596 11.8904Z" fill="white"/>
<path d="M20.5408 8.69802C20.7804 8.55027 21.0691 8.51564 21.3016 8.6072C21.4072 8.64948 21.4989 8.72645 21.5623 8.82498C21.6257 8.91653 21.6543 9.0365 21.6473 9.14919L21.1678 17.0857C21.1043 18.1146 20.7237 19.1578 20.0965 20.1092C18.2852 22.8578 14.5497 24.1683 11.7586 23.0408C10.9693 22.7237 10.3415 22.2448 9.89045 21.6599C9.99617 21.667 10.1021 21.6668 10.2078 21.6668C13.0833 21.6667 15.9657 20.1091 17.5516 17.699C18.3339 16.522 18.7787 15.2387 18.8563 13.9842L19.1238 9.56521L20.5408 8.69802Z" fill="white"/>
<path d="M16.826 5.51052C17.0656 5.36252 17.3551 5.32808 17.5877 5.4197H17.5809C17.6864 5.46201 17.7783 5.53211 17.8416 5.63064C17.905 5.72929 17.9326 5.84213 17.9256 5.95486L17.4471 13.8914C17.3836 14.9202 17.002 15.9564 16.3748 16.9148C14.5633 19.6632 10.8349 20.9672 8.03693 19.8396C5.253 18.7189 4.45701 15.5824 6.26838 12.8338C6.89567 11.8823 7.80501 11.0362 8.82697 10.409L16.826 5.51052Z" fill="white"/>
<path d="M46.708 8.17871C48.471 8.17873 49.8732 8.58974 50.915 9.41113C51.9769 10.2325 52.6082 11.3642 52.8086 12.8066H49.1719C48.9514 11.7049 48.1201 11.1543 46.6777 11.1543C45.9565 11.1543 45.3958 11.2947 44.9951 11.5752C44.6145 11.8557 44.4238 12.2062 44.4238 12.627C44.424 13.0675 44.7142 13.4183 45.2949 13.6787C45.8759 13.9392 46.6478 14.179 47.6094 14.3994C48.6512 14.6398 49.6033 14.9104 50.4648 15.2109C51.3463 15.4914 52.0475 15.9229 52.5684 16.5039C53.0891 17.0648 53.3496 17.8759 53.3496 18.9375C53.3696 19.8591 53.1288 20.6913 52.6279 21.4326C52.1271 22.1737 51.4063 22.7551 50.4648 23.1758C49.5232 23.5965 48.4111 23.8066 47.1289 23.8066C45.8067 23.8066 44.6443 23.5965 43.6426 23.1758C42.6408 22.735 41.8393 22.1334 41.2383 21.3721C40.6372 20.6107 40.2765 19.7293 40.1562 18.7275H44.0332C44.1534 19.3085 44.4742 19.8097 44.9951 20.2305C45.536 20.631 46.2271 20.831 47.0684 20.8311C47.9097 20.8311 48.5207 20.6608 48.9014 20.3203C49.302 19.9798 49.5029 19.5891 49.5029 19.1484C49.5029 18.5073 49.2221 18.0759 48.6611 17.8555C48.1002 17.6151 47.3188 17.3854 46.3174 17.165C45.6762 17.0248 45.0245 16.8537 44.3633 16.6533C43.7023 16.453 43.0911 16.2028 42.5303 15.9023C41.9894 15.5818 41.5486 15.181 41.208 14.7002C40.8675 14.1994 40.6973 13.5883 40.6973 12.8672C40.6973 11.5449 41.218 10.4328 42.2598 9.53125C43.3217 8.62965 44.8046 8.17871 46.708 8.17871ZM60.332 16.5938C60.3321 17.8758 60.5934 18.8579 61.1143 19.5391C61.6352 20.2199 62.4564 20.5605 63.5781 20.5605C64.64 20.5605 65.5112 20.1793 66.1924 19.418C66.8936 18.6566 67.2451 17.5948 67.2451 16.2324V8.53906H71.0918V23.4453H67.6953L67.3945 20.9209C66.9338 21.8023 66.2631 22.5035 65.3818 23.0244C64.5203 23.5453 63.5078 23.8066 62.3457 23.8066C60.4827 23.8066 59.0403 23.2253 58.0186 22.0635C57.0168 20.9014 56.5156 19.198 56.5156 16.9541V8.53906H60.332V16.5938ZM88.7939 8.17871C90.557 8.17873 91.9592 8.58974 93.001 9.41113C94.0628 10.2325 94.6941 11.3642 94.8945 12.8066H91.2578C91.0373 11.7049 90.2061 11.1543 88.7637 11.1543C88.0424 11.1543 87.4817 11.2947 87.0811 11.5752C86.7004 11.8557 86.5098 12.2062 86.5098 12.627C86.5099 13.0675 86.8002 13.4183 87.3809 13.6787C87.9619 13.9392 88.7337 14.179 89.6953 14.3994C90.7372 14.6398 91.6893 14.9104 92.5508 15.2109C93.4323 15.4914 94.1334 15.9229 94.6543 16.5039C95.175 17.0648 95.4355 17.8759 95.4355 18.9375C95.4556 19.8591 95.2148 20.6913 94.7139 21.4326C94.213 22.1737 93.4922 22.7551 92.5508 23.1758C91.6092 23.5965 90.497 23.8066 89.2148 23.8066C87.8926 23.8066 86.7302 23.5965 85.7285 23.1758C84.7267 22.735 83.9253 22.1334 83.3242 21.3721C82.7232 20.6107 82.3624 19.7293 82.2422 18.7275H86.1191C86.2394 19.3085 86.5602 19.8097 87.0811 20.2305C87.6219 20.631 88.3131 20.831 89.1543 20.8311C89.9956 20.8311 90.6066 20.6608 90.9873 20.3203C91.3879 19.9798 91.5888 19.5891 91.5889 19.1484C91.5889 18.5073 91.3081 18.0759 90.7471 17.8555C90.1861 17.6151 89.4048 17.3854 88.4033 17.165C87.7622 17.0248 87.1104 16.8537 86.4492 16.6533C85.7882 16.453 85.1771 16.2028 84.6162 15.9023C84.0753 15.5818 83.6345 15.181 83.2939 14.7002C82.9534 14.1994 82.7832 13.5883 82.7832 12.8672C82.7832 11.5449 83.304 10.4328 84.3457 9.53125C85.4076 8.62965 86.8906 8.17871 88.7939 8.17871ZM106.144 8.17871C108.047 8.17877 109.65 8.67992 110.952 9.68164C112.254 10.6634 113.086 12.0257 113.446 13.7686H109.389C109.188 13.0474 108.788 12.4866 108.187 12.0859C107.606 11.6652 106.915 11.4541 106.113 11.4541C105.051 11.4541 104.149 11.8549 103.408 12.6562C102.667 13.4576 102.296 14.5699 102.296 15.9922C102.296 17.4145 102.667 18.5267 103.408 19.3281C104.15 20.1295 105.051 20.5303 106.113 20.5303C106.915 20.5303 107.606 20.3303 108.187 19.9297C108.788 19.529 109.188 18.958 109.389 18.2168H113.446C113.086 19.8995 112.254 21.2517 110.952 22.2734C109.65 23.2952 108.047 23.8066 106.144 23.8066C104.621 23.8066 103.278 23.4756 102.116 22.8145C100.954 22.1533 100.033 21.2318 99.3516 20.0498C98.6904 18.8677 98.3594 17.5149 98.3594 15.9922C98.3594 14.4696 98.6904 13.1175 99.3516 11.9355C100.033 10.7535 100.954 9.83206 102.116 9.1709C103.278 8.50972 104.621 8.17871 106.144 8.17871ZM123.687 8.17871C125.81 8.17871 127.484 8.7096 128.706 9.77148C129.928 10.8333 130.539 12.336 130.539 14.2793V23.4463H127.264L126.902 21.042C126.462 21.8432 125.84 22.5046 125.039 23.0254C124.258 23.5462 123.246 23.8066 122.004 23.8066C120.722 23.8066 119.67 23.6058 118.849 23.2051C118.027 22.7843 117.415 22.2339 117.015 21.5527C116.614 20.8716 116.414 20.1201 116.414 19.2988C116.414 17.9164 116.955 16.7941 118.037 15.9326C119.119 15.0711 120.742 14.6407 122.905 14.6406H126.692V14.2793C126.692 13.2576 126.401 12.5062 125.82 12.0254C125.239 11.5448 124.518 11.3047 123.657 11.3047C122.876 11.3047 122.194 11.4944 121.613 11.875C121.032 12.2356 120.672 12.7768 120.531 13.498H116.774C116.875 12.4162 117.235 11.4743 117.856 10.6729C118.498 9.87147 119.32 9.26058 120.321 8.83984C121.323 8.39919 122.445 8.17877 123.687 8.17871ZM79.0391 23.4463H75.1914V8.54004H79.0391V23.4463ZM142.986 8.17871C144.83 8.17871 146.262 8.75981 147.284 9.92188C148.306 11.0839 148.816 12.7873 148.816 15.0312V23.4463H144.97V15.3916C144.97 14.1093 144.709 13.1275 144.188 12.4463C143.668 11.7651 142.856 11.4248 141.754 11.4248C140.672 11.4249 139.78 11.8052 139.079 12.5664C138.398 13.3277 138.058 14.3897 138.058 15.752V23.4463H134.211V8.53906H137.607L137.907 11.0635C138.368 10.1821 139.029 9.48084 139.891 8.95996C140.772 8.43907 141.804 8.17874 142.986 8.17871ZM123.326 17.0449C122.305 17.0449 121.574 17.2348 121.133 17.6152C120.692 17.9759 120.472 18.4269 120.472 18.9678C120.472 19.5488 120.692 19.9997 121.133 20.3203C121.574 20.6408 122.155 20.8008 122.876 20.8008C123.978 20.8007 124.829 20.4399 125.43 19.7188C126.051 18.9976 126.442 18.1066 126.603 17.0449H123.326ZM77.1152 1.80762C77.8164 1.80762 78.3874 2.01786 78.8281 2.43848C79.2889 2.83919 79.5195 3.36081 79.5195 4.00195C79.5194 4.6429 79.2888 5.17408 78.8281 5.59473C78.3874 6.01531 77.8163 6.22558 77.1152 6.22559C76.4141 6.22559 75.8329 6.01537 75.3721 5.59473C74.9314 5.17408 74.711 4.6429 74.7109 4.00195C74.7109 3.36081 74.9313 2.83919 75.3721 2.43848C75.8329 2.01779 76.414 1.80762 77.1152 1.80762Z" fill="#1D2332"/>
</svg></a>
                    </div>
                    <nav class="navbar">
                        <ul class="nav-links">
                            <li><a href="#" class="active">Home</a></li>
                            <li><a href="#">Blockchain</a></li>
                            <li><a href="#">Apps</a></li>
                            <li><a href="#">DeFi</a></li>
                            <li><a href="#">Coins</a></li>
                            <li><a href="#">NFTs</a></li>
                            <li><a href="#">Analytics</a></li>
                            <li><a href="#">More</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="home-page">
            <!-- Banner -->
            <div class="banner">
                <div class="container">
                    <div class="banner-content">
                        <div class="banner-text">
                            <span>Explore</span><br> Ika mainnet
                        </div>
                        <div class="banner-logo">
                            <img src="flow.png" alt="Flow" />
                        </div>
                        <div class="banner-text">
                            <span>on</span> <strong><bold>Ikascan</bold></strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Explorer Plate -->
            <div class="explorer-plate">
                <div class="title">Welcome to Sui Universe</div>
                <div class="search-container">
                    <div class="search-icon">🔍</div>
                    <span class="search-text">Search anything on Sui</span>
                    <span class="search-shortcut">/</span>
                </div>
                
                <!-- Trending Section -->
                <div class="trending-wrapper">
                    <span class="trending-label">Trending:</span>
                    <div class="trending-coins">
                        <div class="coin-item sui">
                            <span class="coin-symbol">#SUI</span>
                        </div>
                        <div class="coin-item blue">
                            <span class="coin-symbol">BLUE</span>
                        </div>
                        <div class="coin-item wbtc">
                            <span class="coin-symbol">wBTC</span>
                        </div>
                        <div class="coin-item cetus">
                            <span class="coin-symbol">CETUS</span>
                        </div>
                        <div class="coin-item usdc">
                            <span class="coin-symbol">USDC</span>
                        </div>
                        <div class="coin-item xsui">
                            <span class="coin-symbol">xSUI</span>
                        </div>
                        <div class="coin-item wal">
                            <span class="coin-symbol">WAL</span>
                        </div>
                        <div class="coin-item deep">
                            <span class="coin-symbol">DEEP</span>
                        </div>
                        <div class="coin-item usdt">
                            <span class="coin-symbol">USDT</span>
                        </div>
                    </div>
                </div>

                <!-- News Section -->
                <div class="news-section">
                    <div class="news-item">
                        <div class="news-icon">📢</div>
                        <span class="news-text">SIKA Listings on July 29</span>
                        <span class="news-category">Ecosystem</span>
                        <span class="news-date">28.07.2025</span>
                        <span class="news-type">Sui News</span>
                        <span class="news-updates">Ecosystem Updates</span>
                    </div>
                    <div class="news-hub">
                        <span class="news-hub-icon">🔥</span>
                        <span class="news-hub-text">News Hub</span>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="container">
                <div class="statistics-grid">
                    <!-- Row 1 -->
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Market Cap</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value">$13,536,919,299</div>
                        <div class="stat-change negative">-7.87%</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Circulating Supply</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value">3,455,015,253 SUI</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Dominance</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value">0.34 %</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Supply</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value">10,000,000,000 SUI</div>
                        <div class="stat-subtext">$39,200,000,000</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Stake</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value">7,357,927,313 SUI</div>
                        <div class="stat-subtext">$28,843,075,068</div>
                    </div>

                    <div class="stat-card chart-card">
                        <div class="stat-header">
                            <span class="stat-label">Sui Price</span>
                        </div>
                        <div class="stat-value">$3.92</div>
                        <div class="stat-change negative">24H -8.07%</div>
                        <div class="price-chart">
                            <span class="chart-label">via CoinGecko</span>
                        </div>
                    </div>

                    <!-- Row 2 -->
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Epoch</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value">838</div>
                        <div class="stat-subtext">new: 12h left</div>
                        <div class="stat-change positive">+9%</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Tx Blocks</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">3,885,026,005</div>
                        <div class="stat-change positive">24H +4,116,897</div>
                        <div class="stat-icon">📊</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Transactions</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">11,194,566,658</div>
                        <div class="stat-change positive">24H +16,426,058</div>
                        <div class="mini-chart">📈</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Checkpoints</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">172,886,549</div>
                        <div class="stat-change positive">24H +370,888</div>
                        <div class="stat-icon">🔗</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">TPS Current</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">24</div>
                        <div class="stat-change positive">+10 today</div>
                        <div class="stat-details">
                            <span>📊 Peak/Month 1,338</span>
                            <span>Avg/Day 32</span>
                        </div>
                        <div class="stat-icon">⚡</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">CPS Current</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">104</div>
                        <div class="stat-change positive">+14 today</div>
                        <div class="stat-details">
                            <span>📊 Peak/Month 8,706</span>
                            <span>Avg/Day 139</span>
                        </div>
                        <div class="stat-icon">⚡</div>
                    </div>
                    <!-- Row 2 -->
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Accounts</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">248,308,641</div>
                        <div class="stat-subtext">new: 24h left</div>
                        <div class="stat-change positive">+20%</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Validators</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">3,885,026,005</div>
                        <div class="stat-change positive">24H +4,116,897</div>
                        <div class="stat-icon">📊</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total NFTs</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">198,007,274</div>
                        <div class="stat-change positive">24H +16,426,058</div>
                        <div class="mini-chart">📈</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Package</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">172,886,549</div>
                        <div class="stat-change positive">24H +370,888</div>
                        <div class="stat-icon">🔗</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total coin</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">150,679</div>
                        <div class="stat-change positive">+10 today</div>
                        <div class="stat-details">
                            <span>📊 Peak/Month 1,338</span>
                            <span>Avg/Day 32</span>
                        </div>
                        <div class="stat-icon">⚡</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Directory Projects</span>
                            <span class="stat-info">ℹ</span>
                        </div>
                        <div class="stat-value blue">846</div>
                        <div class="stat-change positive">+14 today</div>
                        <div class="stat-details">
                            <span>📊 Peak/Month 8,706</span>
                            <span>Avg/Day 139</span>
                        </div>
                        <div class="stat-icon">⚡</div>
                    </div>
                    
                </div>
            </div>

            <!-- DeFi, DEX Pools, and Collections Section -->
            <div class="defi-section-wrapper">
                <div class="container">
                    <div class="defi-section">
                        <!-- DeFi Column -->
                        <div class="defi-column">
                            <h3 class="section-title">DeFi</h3>
                            <div class="defi-list">
                                <div class="defi-item">
                                    <div class="defi-icon">
                                        <svg width="32" height="32" viewBox="0 0 32 32" class="protocol-icon">
                                            <circle cx="16" cy="16" r="16" fill="#4F46E5"/>
                                            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">S</text>
                                        </svg>
                                    </div>
                                    <div class="defi-info">
                                        <div class="defi-name">Suilend</div>
                                        <div class="defi-tvl">TVL $693,133,195</div>
                                    </div>
                                </div>

                                <div class="defi-item">
                                    <div class="defi-icon">
                                        <svg width="32" height="32" viewBox="0 0 32 32" class="protocol-icon">
                                            <circle cx="16" cy="16" r="16" fill="#10B981"/>
                                            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">N</text>
                                        </svg>
                                    </div>
                                    <div class="defi-info">
                                        <div class="defi-name">NAVI Protocol</div>
                                        <div class="defi-tvl">TVL $684,300,780</div>
                                    </div>
                                </div>

                                <div class="defi-item">
                                    <div class="defi-icon">
                                        <svg width="32" height="32" viewBox="0 0 32 32" class="protocol-icon">
                                            <circle cx="16" cy="16" r="16" fill="#3B82F6"/>
                                            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">S</text>
                                        </svg>
                                    </div>
                                    <div class="defi-info">
                                        <div class="defi-name">SpringSui</div>
                                        <div class="defi-tvl">TVL $272,875,785</div>
                                    </div>
                                </div>

                                <div class="defi-item">
                                    <div class="defi-icon">
                                        <svg width="32" height="32" viewBox="0 0 32 32" class="protocol-icon">
                                            <circle cx="16" cy="16" r="16" fill="#F59E0B"/>
                                            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">H</text>
                                        </svg>
                                    </div>
                                    <div class="defi-info">
                                        <div class="defi-name">Haedal</div>
                                        <div class="defi-tvl">TVL $192,300,102</div>
                                    </div>
                                </div>

                                <div class="defi-item">
                                    <div class="defi-icon">
                                        <svg width="32" height="32" viewBox="0 0 32 32" class="protocol-icon">
                                            <circle cx="16" cy="16" r="16" fill="#6366F1"/>
                                            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">B</text>
                                        </svg>
                                    </div>
                                    <div class="defi-info">
                                        <div class="defi-name">Bluefin</div>
                                        <div class="defi-tvl">TVL $183,129,733</div>
                                    </div>
                                </div>
                            </div>
                            <div class="section-more">
                                <a href="#" class="more-link">More ›</a>
                            </div>
                        </div>

                    <!-- Top DEX Pools Column -->
                    <div class="dex-column">
                        <h3 class="section-title">Top DEX Pools <span class="time-indicator">24H</span></h3>
                        <div class="dex-list">
                            <div class="dex-item">
                                <div class="dex-icons">
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#2775CA"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">U</text>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#26A17B"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">T</text>
                                    </svg>
                                    <svg width="16" height="16" viewBox="0 0 16 16" class="token-icon small">
                                        <circle cx="8" cy="8" r="8" fill="#4F46E5"/>
                                        <text x="8" y="11" text-anchor="middle" fill="white" font-size="6" font-weight="bold">C</text>
                                    </svg>
                                </div>
                                <div class="dex-info">
                                    <div class="dex-pair">Pool:USDC / USDT</div>
                                    <div class="dex-volume">Vol. $38,495,209 <span class="change positive">+12%</span></div>
                                </div>
                            </div>

                            <div class="dex-item">
                                <div class="dex-icons">
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#26A17B"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">T</text>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#2775CA"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">U</text>
                                    </svg>
                                    <svg width="16" height="16" viewBox="0 0 16 16" class="token-icon small">
                                        <circle cx="8" cy="8" r="8" fill="#3B82F6"/>
                                        <text x="8" y="11" text-anchor="middle" fill="white" font-size="6" font-weight="bold">B</text>
                                    </svg>
                                </div>
                                <div class="dex-info">
                                    <div class="dex-pair">Pool:USDT / USDC</div>
                                    <div class="dex-volume">Vol. $38,287,038 <span class="change positive">+8%</span></div>
                                </div>
                            </div>

                            <div class="dex-item">
                                <div class="dex-icons">
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#2775CA"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">U</text>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#4F46E5"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">S</text>
                                    </svg>
                                    <svg width="16" height="16" viewBox="0 0 16 16" class="token-icon small">
                                        <circle cx="8" cy="8" r="8" fill="#4F46E5"/>
                                        <text x="8" y="11" text-anchor="middle" fill="white" font-size="6" font-weight="bold">C</text>
                                    </svg>
                                </div>
                                <div class="dex-info">
                                    <div class="dex-pair">Pool:USDC / SUI</div>
                                    <div class="dex-volume">Vol. $30,259,575 <span class="change positive">+20%</span></div>
                                </div>
                            </div>

                            <div class="dex-item">
                                <div class="dex-icons">
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#4F46E5"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">S</text>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#2775CA"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">U</text>
                                    </svg>
                                    <svg width="16" height="16" viewBox="0 0 16 16" class="token-icon small">
                                        <circle cx="8" cy="8" r="8" fill="#8B5CF6"/>
                                        <text x="8" y="11" text-anchor="middle" fill="white" font-size="6" font-weight="bold">M</text>
                                    </svg>
                                </div>
                                <div class="dex-info">
                                    <div class="dex-pair">Pool:SUI / USDC</div>
                                    <div class="dex-volume">Vol. $28,596,204 <span class="change positive">+6%</span></div>
                                </div>
                            </div>

                            <div class="dex-item">
                                <div class="dex-icons">
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#26A17B"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">T</text>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 20 20" class="token-icon">
                                        <circle cx="10" cy="10" r="10" fill="#2775CA"/>
                                        <text x="10" y="13" text-anchor="middle" fill="white" font-size="8" font-weight="bold">U</text>
                                    </svg>
                                    <svg width="16" height="16" viewBox="0 0 16 16" class="token-icon small">
                                        <circle cx="8" cy="8" r="8" fill="#8B5CF6"/>
                                        <text x="8" y="11" text-anchor="middle" fill="white" font-size="6" font-weight="bold">M</text>
                                    </svg>
                                </div>
                                <div class="dex-info">
                                    <div class="dex-pair">Pool:USDT / USDC</div>
                                    <div class="dex-volume">Vol. $25,163,694 <span class="change negative">-34%</span></div>
                                </div>
                            </div>
                        </div>
                        <div class="section-more">
                            <a href="#" class="more-link">More ›</a>
                        </div>
                    </div>

                    <!-- Trending Collections Column -->
                    <div class="collections-column">
                        <h3 class="section-title">Trending Collections <span class="time-indicator">24H</span></h3>
                        <div class="collections-list">
                            <div class="collection-item">
                                <div class="collection-icon">
                                    <svg width="32" height="32" viewBox="0 0 32 32" class="collection-image">
                                        <circle cx="16" cy="16" r="16" fill="#10B981"/>
                                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">P</text>
                                    </svg>
                                </div>
                                <div class="collection-info">
                                    <div class="collection-name">Pawtato Land</div>
                                    <div class="collection-volume">Vol. $1,766</div>
                                </div>
                            </div>

                            <div class="collection-item">
                                <div class="collection-icon">
                                    <svg width="32" height="32" viewBox="0 0 32 32" class="collection-image">
                                        <circle cx="16" cy="16" r="16" fill="#F59E0B"/>
                                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">P</text>
                                    </svg>
                                </div>
                                <div class="collection-info">
                                    <div class="collection-name">Popkins</div>
                                    <div class="collection-volume">Vol. $911</div>
                                </div>
                            </div>

                            <div class="collection-item">
                                <div class="collection-icon">
                                    <svg width="32" height="32" viewBox="0 0 32 32" class="collection-image">
                                        <circle cx="16" cy="16" r="16" fill="#8B5CF6"/>
                                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">B</text>
                                    </svg>
                                </div>
                                <div class="collection-info">
                                    <div class="collection-name">Bored Toilet Club</div>
                                    <div class="collection-volume">Vol. $631</div>
                                </div>
                            </div>

                            <div class="collection-item">
                                <div class="collection-icon">
                                    <svg width="32" height="32" viewBox="0 0 32 32" class="collection-image">
                                        <circle cx="16" cy="16" r="16" fill="#1F2937"/>
                                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">P</text>
                                    </svg>
                                </div>
                                <div class="collection-info">
                                    <div class="collection-name">Prime Machin</div>
                                    <div class="collection-volume">Vol. $572</div>
                                </div>
                            </div>

                            <div class="collection-item">
                                <div class="collection-icon">
                                    <svg width="32" height="32" viewBox="0 0 32 32" class="collection-image">
                                        <circle cx="16" cy="16" r="16" fill="#6366F1"/>
                                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">M</text>
                                    </svg>
                                </div>
                                <div class="collection-info">
                                    <div class="collection-name">Mystic Yeti</div>
                                    <div class="collection-volume">Vol. $327</div>
                                </div>
                            </div>
                        </div>
                        <div class="section-more">
                            <a href="#" class="more-link">More ›</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Statistics with Charts -->
            <div class="container">
                <div class="bottom-stats">
                    <div class="stat-chart-item">
                        <div class="stat-chart-header">
                            <span class="stat-chart-label">TVL</span>
                            <span class="stat-chart-info">ℹ</span>
                        </div>
                        <div class="stat-chart-value">$2,181,291,063</div>
                        <div class="stat-chart-period">ALL</div>
                        <div class="mini-chart-container">
                            <div class="trend-chart">
                                <div class="trend-line trend-up"></div>
                                <div class="trend-dots">
                                    <span class="dot" style="left: 10%; bottom: 20%"></span>
                                    <span class="dot" style="left: 25%; bottom: 35%"></span>
                                    <span class="dot" style="left: 40%; bottom: 50%"></span>
                                    <span class="dot" style="left: 55%; bottom: 65%"></span>
                                    <span class="dot" style="left: 70%; bottom: 75%"></span>
                                    <span class="dot" style="left: 85%; bottom: 85%"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-chart-item">
                        <div class="stat-chart-header">
                            <span class="stat-chart-label">New Accounts</span>
                            <span class="stat-chart-info">ℹ</span>
                        </div>
                        <div class="stat-chart-value">8,750,933</div>
                        <div class="stat-chart-period">7D</div>
                        <div class="mini-chart-container">
                            <div class="trend-chart">
                                <div class="trend-line trend-moderate"></div>
                                <div class="trend-dots">
                                    <span class="dot" style="left: 10%; bottom: 30%"></span>
                                    <span class="dot" style="left: 25%; bottom: 25%"></span>
                                    <span class="dot" style="left: 40%; bottom: 45%"></span>
                                    <span class="dot" style="left: 55%; bottom: 55%"></span>
                                    <span class="dot" style="left: 70%; bottom: 60%"></span>
                                    <span class="dot" style="left: 85%; bottom: 70%"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-chart-item">
                        <div class="stat-chart-header">
                            <span class="stat-chart-label">New NFTs</span>
                            <span class="stat-chart-info">ℹ</span>
                        </div>
                        <div class="stat-chart-value">1,194,629</div>
                        <div class="stat-chart-period">7D</div>
                        <div class="mini-chart-container">
                            <div class="trend-chart">
                                <div class="trend-line trend-volatile"></div>
                                <div class="trend-dots">
                                    <span class="dot" style="left: 10%; bottom: 40%"></span>
                                    <span class="dot" style="left: 25%; bottom: 60%"></span>
                                    <span class="dot" style="left: 40%; bottom: 35%"></span>
                                    <span class="dot" style="left: 55%; bottom: 70%"></span>
                                    <span class="dot" style="left: 70%; bottom: 45%"></span>
                                    <span class="dot" style="left: 85%; bottom: 65%"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-chart-item">
                        <div class="stat-chart-header">
                            <span class="stat-chart-label">New Coins</span>
                            <span class="stat-chart-info">ℹ</span>
                        </div>
                        <div class="stat-chart-value">1,881</div>
                        <div class="stat-chart-period">7D</div>
                        <div class="mini-chart-container">
                            <img src="https://via.placeholder.com/120x40/f8fafc/7e9bfc?text=🪙" alt="Coins Chart" class="chart-image">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Latest News Section -->
            <div class="container">
                <div class="latest-news">
                    <div class="news-header">
                        <div class="volume-icon">📢</div>
                        <span>Latest Updates</span>
                    </div>
                    <div class="news-content">
                        <p>Stay updated with the latest blockchain developments and network statistics.</p>
                    </div>
                </div>
            </div>

            <!-- Main Content Sections -->
            <div class="container">
                <div class="main-sections">
                    <div class="section-column">
                        <div class="section-item">
                            <span class="section-icon">📖</span>
                            <span class="section-title">Sui Explained</span>
                        </div>
                        <div class="section-item">
                            <span class="section-icon">🌐</span>
                            <span class="section-title">SuiNS Domains</span>
                        </div>
                        <div class="section-item">
                            <span class="section-icon">✅</span>
                            <span class="section-title">Verify Package</span>
                        </div>
                        <div class="section-item">
                            <span class="section-icon">⚡</span>
                            <span class="section-title">Execute Transaction</span>
                        </div>
                    </div>

                    <div class="section-column">
                        <div class="section-item">
                            <span class="section-icon">🚰</span>
                            <span class="section-title">Sui Faucet</span>
                        </div>
                        <div class="section-item">
                            <span class="section-icon">🔌</span>
                            <span class="section-title">Sui API</span>
                        </div>
                        <div class="section-item">
                            <span class="section-icon">📊</span>
                            <span class="section-title">Sui Metadata API</span>
                        </div>
                        <div class="section-item">
                            <span class="section-icon">🔒</span>
                            <span class="section-title">Sui Security API</span>
                        </div>
                    </div>

                    <div class="section-column partners">
                        <div class="partner-item">
                            <div class="partner-logo">
                                <div class="logo-circle black">A</div>
                            </div>
                            <div class="partner-info">
                                <div class="partner-name">Artipedia</div>
                                <div class="partner-desc">Social & Community</div>
                            </div>
                        </div>

                        <div class="partner-item">
                            <div class="partner-logo">
                                <div class="logo-circle blue">R</div>
                            </div>
                            <div class="partner-info">
                                <div class="partner-name">Rango</div>
                                <div class="partner-desc">DeFi · DEX Aggregator</div>
                            </div>
                        </div>

                        <div class="partner-item">
                            <div class="partner-logo">
                                <div class="logo-circle dark">C</div>
                            </div>
                            <div class="partner-info">
                                <div class="partner-name">CCTOO</div>
                                <div class="partner-desc">Meme Coins</div>
                            </div>
                        </div>

                        <div class="partner-item">
                            <div class="partner-logo">
                                <div class="logo-circle orange">🏆</div>
                            </div>
                            <div class="partner-info">
                                <div class="partner-name">Community Take Over Rugs</div>
                                <div class="partner-desc">Meme Coins</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Logo and Description -->
                <div class="footer-brand">
                    <div class="footer-logo">
                        <img src="logo.svg" alt="Suiscan Logo">
                    </div>
                    <p class="footer-description">
                        Suiscan is a feature-rich block explorer and analytics platform for Sui.
                    </p>
                </div>

                <!-- Footer Links -->
                <div class="footer-links">
                    <div class="footer-column">
                        <h4>Explorer</h4>
                        <ul>
                            <li><a href="#">News</a></li>
                            <li><a href="#">Get Listed</a></li>
                            <li><a href="#">API</a></li>
                            <li><a href="#">Partner Referrals</a></li>
                            <li><a href="#">Bug Report</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h4>Company</h4>
                        <ul>
                            <li><a href="#">Support us 💖</a></li>
                            <li><a href="#">Branding Assets</a></li>
                            <li><a href="#">Terms of Service</a></li>
                            <li><a href="#">Privacy Policy</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h4>Social</h4>
                        <ul>
                            <li><a href="#"><span class="social-icon">📱</span> Github</a></li>
                            <li><a href="#"><span class="social-icon">🐦</span> Twitter</a></li>
                            <li><a href="#"><span class="social-icon">💬</span> Discord</a></li>
                            <li><a href="#"><span class="social-icon">📧</span> Email</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="footer-credits">
                    <div class="credit-section">
                        <span class="credit-label">BY</span>
                        <div class="credit-logo">
                            <span class="staketab-logo">🔷 STAKETAB</span>
                        </div>
                    </div>

                    <div class="credit-section">
                        <span class="credit-label">BUILT WITH</span>
                        <div class="credit-logo">
                            <span class="blockberry-logo">🫐 blockberry</span>
                        </div>
                    </div>

                    <div class="credit-section">
                        <span class="credit-label">FEATURED ON</span>
                        <div class="featured-logos">
                            <span class="featured-logo">📊 CoinMarketCap</span>
                            <span class="featured-logo">🎁 Staking Rewards</span>
                            <span class="featured-logo">🦎 CoinGecko</span>
                            <span class="featured-logo">🔍 Gecko Terminal</span>
                            <span class="featured-logo">📈 DEXSCREENER</span>
                            <span class="featured-logo">🦙 DefiLlama</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
